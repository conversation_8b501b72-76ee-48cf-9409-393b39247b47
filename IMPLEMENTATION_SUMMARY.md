# 抽帧任务接口实现总结

## 实现概述

成功新增了 `POST /internal/v1/snaptasks` 接口，用于创建视频流抽帧任务。该接口能够对指定的视频流URL进行拉流，并根据设定的时间间隔进行抽帧处理。

## 修改的文件

### 1. `schema/data_model.py`
- 新增 `StreamInfo` 类：定义流信息结构
- 新增 `SnapTaskRequest` 类：定义抽帧任务请求体结构

### 2. `api.py`
- 导入新的数据模型 `SnapTaskRequest`
- 导入新的任务处理函数 `start_snap_task`
- 新增 `POST /internal/v1/snaptasks` 接口实现

### 3. `call_back.py`
- 新增 `start_snap_task()` 函数：启动抽帧任务
- 新增 `callback_snap_task()` 函数：处理抽帧任务回调
- 修改 `call_back()` 函数：添加对抽帧任务的识别和路由

### 4. 新增测试和文档文件
- `test_snap_task.py`：测试脚本
- `SNAP_TASK_API.md`：API文档
- `IMPLEMENTATION_SUMMARY.md`：实现总结

## 核心功能实现

### 1. 请求体结构
```json
{
  "id": "task_20241201_001",
  "stream": {
    "id": "stream_camera_01",
    "source": "rtmp_camera",
    "url": "rtmp://*************:1935/live/camera01",
    "urlRefreshCallback": "http://example.com/refresh",
    "urlRefreshInterval": 3600,
    "urlRefreshStreamPath": "$.data.url"
  },
  "interval": 30.0,
  "forwardReq": false
}
```

### 2. 抽帧频率计算
- 根据 `interval` 参数计算抽帧频率：`frame_rate = 1.0 / interval`
- 限制帧率范围：0.001-25 fps
- 示例：interval=30秒 → 0.033fps（每30秒抽一帧）

### 3. 任务处理流程
1. 验证请求体格式
2. 检查任务ID是否已存在，如存在则停止旧任务
3. 缓存任务内容到Redis
4. 调用底层分析服务启动抽帧任务
5. 更新任务状态为运行中
6. 返回成功响应

### 4. 回调处理
- 任务初始化：记录日志
- 任务失败：更新状态为失败
- 任务完成：更新状态为完成
- 抽帧进行中：记录抽取的帧信息

## 技术特点

### 1. 兼容性
- 复用现有的任务管理和回调机制
- 使用现有的Redis缓存和状态管理
- 集成现有的gRPC分析服务

### 2. 灵活性
- 支持多种视频流格式（RTMP/RTSP/HTTP）
- 支持流地址刷新机制
- 可配置的抽帧间隔

### 3. 可靠性
- 任务状态跟踪和管理
- 错误处理和状态更新
- 任务冲突检测和处理

### 4. 可扩展性
- 模块化设计，易于扩展
- 标准化的数据模型
- 完整的回调处理机制

## 使用示例

### cURL 请求
```bash
curl -X POST http://localhost:3001/internal/v1/snaptasks \
  -H "Content-Type: application/json" \
  -d '{
    "id": "task_20241201_001",
    "stream": {
      "id": "stream_camera_01",
      "source": "rtmp_camera",
      "url": "rtmp://*************:1935/live/camera01"
    },
    "interval": 30.0,
    "forwardReq": false
  }'
```

### 成功响应
```json
{
  "code": 200,
  "msg": "ok",
  "data": {
    "taskId": "task_20241201_001"
  }
}
```

## 测试验证

- 创建了完整的测试脚本 `test_snap_task.py`
- 测试不同间隔时间的抽帧频率计算
- 验证请求体格式和响应格式
- 所有Python文件编译通过，无语法错误

## 部署说明

1. 确保所有依赖包已安装（fastapi, pydantic, uvicorn等）
2. 启动服务：`python api.py`
3. 接口地址：`http://localhost:3001/internal/v1/snaptasks`
4. 使用测试脚本验证功能：`python test_snap_task.py`

## 注意事项

1. 任务ID必须唯一，重复ID会导致旧任务被停止
2. interval参数必须大于0，建议不要设置过小的值
3. 视频流URL必须可访问，否则任务会失败
4. 抽取的帧会自动上传到S3存储，默认保存7天
5. 需要确保底层分析服务正常运行

## 后续优化建议

1. 添加更详细的错误码和错误信息
2. 支持批量任务创建
3. 添加任务优先级管理
4. 支持更多的视频流参数配置
5. 添加任务执行统计和监控
