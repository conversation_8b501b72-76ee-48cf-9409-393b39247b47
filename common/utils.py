import asyncio
import base64
import os
from uuid import uuid4

import aiofiles

from common.exceptions import TooManyTriesException


def image_matting_v2(x, y, w, h, max_x, max_y, scale: float = 0.3):
    assert max_x > 0, "最大x不合法"
    assert max_y > 0, "最大y不合法"
    edge_scale = scale * min(w, h)

    x1 = max(0, int(x - edge_scale))
    y1 = max(0, int(y - edge_scale))
    w = max(0, min(max_x - x1, w + 2 * edge_scale))
    h = max(0, min(max_y - y1, h + 2 * edge_scale))

    return list(map(int, [x1, y1, w, h]))


def retry(times, delay=0.5):
    def func_wrapper(f):
        async def wrapper(*args, **kwargs):
            exc = None
            for time in range(times):
                try:
                    return await f(*args, **kwargs)
                except Exception as e:
                    await asyncio.sleep(delay)
                    exc = e
            raise TooManyTriesException() from exc

        return wrapper

    return func_wrapper


async def trans_image2img_abspath(image_dir, image_base64, timestamp):
    async def save2file(_img_bytes: bytes):
        os.makedirs(image_dir, exist_ok=True)
        _img_path = f"{image_dir}/{timestamp}_{uuid4()}.jpg"
        async with aiofiles.open(_img_path, "wb") as f:
            await f.write(_img_bytes)
        return _img_path

    img_path = await save2file(base64.b64decode(image_base64))

    return img_path


def convert_detect_conf(original_json):
    face_fields = ['iPitch', 'iRoll', 'iYaw', 'iDefinition', 'iIntegrity']
    other_fields = ['iShadeDegree', 'iTruncationDegree']

    def convert_item(key, value):
        item = {
            'iEnable': int(value['enabled']),
            'aRegin': value.get('regionArray', []),
            'aSize': {'iWidth': value['minWidth'], 'iHeight': value['minHeight']},
            'iConfidence': int(value['confidence']),
            "iDefinition": int(value['clear']),
            "iTruncationDegree": int(value['truncation']),
            "iShadeDegree": int(value['occlusion'])
        }
        angle = value.get('captureCondition', {})
        if angle:
            for key, value in angle.items():
                key = f"i{key.capitalize()}"
                item.update({key + "Max": value[1], key + "Min": value[0]})
        for sub_regin in item["aRegin"]:
            for point in sub_regin:
                point['x'] = int(point['x'])
                point['y'] = int(point['y'])
        if key == "face":
            item['iFqaScore'] = int(value.get("qaScore"))
        if key == 'vehicle':
            item['iPlateEnable'] = int(value.get("plateEnable"))
            item['iPlateConfidence'] = int(value.get("plateConfidence"))

        # item.update({k: value.get(k[1].lower() + k[2:]) for k in face_fields})
        # else:
        #     item.update({k: value.get(k[1].lower() + k[2:]) for k in other_fields})
        return item

    return {
        'detectConf': {
            key: convert_item(key, value)
            for key, value in original_json['detectConf'].items()
        }
    }
