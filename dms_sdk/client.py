# coding: utf-8

import hashlib
import hmac
import json
import time
import uuid
import hashlib
import aiohttp
from logger import logger
from common.typevars import DeviceType
from common.utils import retry
from settings import CntEnvConstant, REQUEST_ID_CONTEXT


class DmsServiceClient:
    """文档: https://sensoro.yuque.com/xud1db/vxeta5/bt5rgx"""

    def __init__(self, app_id=CntEnvConstant.SYS_ID, app_secret=CntEnvConstant.SYS_SECRET):
        self.app_id = app_id
        self.app_secret = app_secret
        self.server_addr = CntEnvConstant.IVMS_DEVICE_SERVICE_URI

        self.headers = {"Content-Type": "application/json"}  # 默认请求头

    def gen_token(self,timestamp: int) -> str:
        md5 = hashlib.md5()
        string = f"username=SENSORO&timestamp={timestamp}"
        # 例: "username=SENSORO&timestamp=1729481402"
        md5.update(string.encode("utf-8"))
        return md5.hexdigest()

    @retry(times=2, delay=0.2)
    async def smart_request(self, url, body, method, headers) -> dict:
        async with aiohttp.ClientSession() as session:
            async with session.request(method=method, url=url, data=body, headers=headers) as resp:
                assert resp.status == 200, f"请求失败, {resp.status}, {resp.reason}"
                return await resp.json()

    async def base_request(self, method, url, body=None):
        if isinstance(body, (dict, list)):
            body = json.dumps(body)

        return await self.smart_request(url=url, body=body, method=method, headers=self.headers)

    async def video_snapshot_capture_callback(self, callback_url, result: dict):
        logger.info(
            f"dms 业务回调=========回调地址：{callback_url}========== appid为: {self.app_id} secret为: {self.app_secret}")
        result['token'] = self.gen_token(result['timestamp'])
        response_body = await self.base_request(
            method="POST", url=callback_url, body=result
        )
        return response_body.get("data", {})


sys_dms_service = DmsServiceClient(CntEnvConstant.SYS_ID, CntEnvConstant.SYS_SECRET)

if __name__ == "__main__":
    import asyncio

    loop = asyncio.get_event_loop()
    r = loop.run_until_complete(
        sys_dms_service.video_snapshot_capture_callback(
            task_id="62dfa81b8d3ee3001956e10a", sn="SNABC", cover_url="http://baidu.com"
        )
    )
    print(r)
