#python3 /app/download_ai_model.py
#wget "https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/models.tar.gz" -O "/app/models.tar.gz" && tar -vxf "/app/models.tar.gz" -C "/app"
#ln -s -f /lib/x86_64-linux-gnu/libffi.so.7.1.0 /root/miniconda/envs/python38_torch201_cuda/lib/libffi.so.7
#wget https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/offline_analysis-0.0.2%2Bklx-cp38-cp38-linux_x86_64.whl && pip install "offline_analysis-0.0.2+klx-cp38-cp38-linux_x86_64.whl"
export  AMS_VSC_GET_URL="https://vsc-get-wx.xn.sensoro.vip"
nohup python3 /app/timed.py