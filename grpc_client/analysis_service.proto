syntax = "proto3";

// 模型加载请求
message LoadModelRequest {
    // 模型路径
    string model_path = 1;
    // 配置名称，默认为"config.json"
    string config_name = 2;
}

// 空消息，用于不需要返回值的操作
message Empty {}

// 开启任务请求
message StartTaskRequest {
    // 任务ID
    string task_id = 1;
    // 任务参数
    string param = 2;
}

// 停止任务请求
message StopTaskRequest {
    // 任务ID
    string task_id = 1;
}

// 查询任务状态请求
message GetTaskStatusRequest {
    // 任务ID (可为空，表示查询所有任务状态)
    string task_id = 1;
}

// 查询任务状态响应
message GetTaskStatusResponse {
    // 任务状态列表
    string task_status = 1;
}

// 查询系统状态响应
message GetSystemStatusResponse {
    // 系统状态
    string system_status = 1;
}

service OfflineAnalysisServer {
    // 加载模型
    rpc LoadModel(LoadModelRequest) returns (Empty);
    // 开始任务
    rpc StartTask(StartTaskRequest) returns (Empty);
    // 停止任务
    rpc StopTask(StopTaskRequest) returns (Empty);
    // 查询任务状态
    rpc GetTaskStatus(GetTaskStatusRequest) returns (GetTaskStatusResponse);
    // 查询系统状态
    rpc GetSystemStatus(Empty) returns (GetSystemStatusResponse);
}