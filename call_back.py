import asyncio
import json
import os
import time
import uuid

from aise_sdk.client import AiseServiceClient
from ams_sdk.client import sys_ivms_service, IvmsServiceClient
from common.typevars import AiAnalyseMode, CaptureSource, TargetType, CallBackTaskStatus, TaskStatus
from common.utils import convert_detect_conf
from dms_sdk.client import DmsServiceClient
from grpc_client.analysis_client import AnalysisClient
from libs.redis_handler import init_redis_pool, Vs2cCacheHandler
from logger import logger
from s3_sdk.base import S3BaseClient
from s3_sdk.s3_conf import EncryptAgent
from schema.data_model import TaskConfig, TaskDefinition

# import offline_analysis as oa

from settings import CntEnvConstant

# oa.load_model("/app/models")
s3_sdk = S3BaseClient()
s3_encrypt_agent = EncryptAgent(public_get_host="https://vsc-get.xn.sensoro.vip")
s3_xn_sdk = S3BaseClient(s3_encrypt_agent=s3_encrypt_agent)
redis = init_redis_pool()

# 回调状态值
'''
// 0-任务不存在 1-任务初始化中 2-文件下载中 3-文件下载失败 
// 4-视频文件不支持或实时流拉取失败 5-视频分析中 6-视频解码失败 7-视频分析完成
'''


class CallBackProcessor:
    def __init__(self, task_config: TaskConfig, task_definition: TaskDefinition, capture_time: int):
        self.task_config = task_config
        self.task_definition = task_definition
        self.capture_time = capture_time

    def get_s3_sdk(self):
        if self.task_config.env == "vxining":
            return s3_xn_sdk
        return s3_sdk

    async def process_image_group(self, capture_image_key, scene_image_key, pos=None, status=None, location=None,
                                  score=None):
        current_s3_sdk = self.get_s3_sdk()
        # 任务初始化进行业务回调
        if status == CallBackTaskStatus.TASK_INITING:
            await IvmsServiceClient(
                app_id=self.task_config.systemAppInfo.id,
                app_secret=self.task_config.systemAppInfo.secret,
            ).video_snapshot_status_callback(
                snapshot_webhook_url=self.task_definition.snapshotWebhookUrl,
                task_id=self.task_definition.taskId,
                status="running"

            )
            logger.info(f"任务开始执行进行回调")
        elif status in [CallBackTaskStatus.TASK_DECODE_ERR, CallBackTaskStatus.TASK_DOWNLOAD_ERR,
                        CallBackTaskStatus.TASK_STREAM_PULL_ERR]:
            Vs2cCacheHandler.cache_task_status(self.task_definition.taskId, json.dumps(
                {"status": TaskStatus.failed, "pod_ip": CntEnvConstant.POD_IP}))
            logger.info(f"任务执行失败存储任务状态")

        elif status == CallBackTaskStatus.TASK_ANALYSIS_DONE:
            # pod 在执行任务数-1
            # Vs2cCacheHandler.cache_pod_task_count(CntEnvConstant.POD_IP, -1)
            # 设置任务的状态为完成
            Vs2cCacheHandler.cache_task_status(self.task_definition.taskId, json.dumps(
                {"status": TaskStatus.complete, "pod_ip": CntEnvConstant.POD_IP}))
            logger.info(f"{self.task_definition.taskId}:任务已经完成，删除对应的任务配置")
        elif status == CallBackTaskStatus.TASK_ANALYSISING and scene_image_key and self.task_definition.mode in [
            AiAnalyseMode.fast_video_ai_mode, AiAnalyseMode.fast_video_ai_mode_aicity, ]:
            scene_url = await current_s3_sdk.sign_s3_agent_url(
                s3_key=scene_image_key,
            )
            resp = await IvmsServiceClient(
                app_id=self.task_config.systemAppInfo.id,
                app_secret=self.task_config.systemAppInfo.secret,
            ).video_snapshot_cover_callback(self.task_definition.snapshotWebhookUrl, self.task_definition.taskId,
                                            self.task_definition.deviceSN,
                                            scene_url)
            logger.debug(f"基础云抓拍封面图回调结果为：{resp}")

        # 极速分析进度回调
        if self.task_definition.mode in [AiAnalyseMode.fast_video_ai_mode, AiAnalyseMode.fast_video_ai_mode_aicity,
                                         AiAnalyseMode.advanced_reanalyse_ai_mode]:
            await IvmsServiceClient(
                app_id=self.task_config.systemAppInfo.id,
                app_secret=self.task_config.systemAppInfo.secret,
            ).video_snapshot_process_callback(
                snapshot_webhook_url=self.task_definition.snapshotWebhookUrl,
                task_id=self.task_definition.taskId,
                pos=pos,
            )
        if not capture_image_key and not scene_image_key:
            logger.info(f"图片路径为空，不进行回调")
            return

        # 基础云抓拍结果回调
        if self.task_definition.mode in [AiAnalyseMode.standard_ai_mode,
                                         AiAnalyseMode.double_analyse_ai_mode] and not capture_image_key:
            # rate = self.task_definition.rate
            # mode 6 两种回调都会有排除高级云的情况
            # if self.task_definition.mode == AiAnalyseMode.double_analyse_ai_mode:
            #     rate = self.task_definition.appRate

            scene_url = await current_s3_sdk.sign_s3_agent_url(
                s3_key=scene_image_key,
            )
            resp = await IvmsServiceClient(
                app_id=self.task_config.systemAppInfo.id,
                app_secret=self.task_config.systemAppInfo.secret,
            ).video_snapshot_capture_callback(self.task_definition.snapshotWebhookUrl, self.task_definition.taskId,
                                              scene_url, self.task_definition.rate, self.capture_time,
                                              self.task_definition.appRate)
            logger.debug(f"基础云抓拍回调结果为：{resp}")

        if not self.task_definition.mode == AiAnalyseMode.standard_ai_mode:
            # mode 6 两种回调都会有排除基础云的回调
            await self.upload_and_push(capture_image_key, scene_image_key, location, score)
            # 离线视频分析回调AISE, 处理进度
            logger.info(
                f"analysis mode: {self.task_definition.mode}, is offline: {self.task_definition.mode == AiAnalyseMode.fast_video_ai_mode}")

    async def upload_and_push(self, capture_image_key: str, scene_image_key: str, location: dict, score: float):
        # mode 6 两种回调都会有排除基础云的回调
        if not location:
            return
        current_s3_sdk = self.get_s3_sdk()
        capture_url, scene_url = await asyncio.gather(
            current_s3_sdk.sign_s3_agent_url(
                s3_key=capture_image_key
            ),
            current_s3_sdk.sign_s3_agent_url(
                s3_key=scene_image_key,
            ),
        )
        logger.info(f"S3 签名完成: {capture_url}, {scene_url}")
        capture_type = "liveStream"
        if self.task_definition.mode in [AiAnalyseMode.advanced_live_ai_mode, AiAnalyseMode.advanced_reanalyse_ai_mode,
                                         AiAnalyseMode.fast_video_ai_mode_aicity, AiAnalyseMode.double_analyse_ai_mode]:
            capture_source = CaptureSource.advance_cloud
            capture_type = "offlineVideo"
        elif self.task_definition.mode == AiAnalyseMode.standard_ai_mode:
            capture_source = CaptureSource.cloud
        else:
            capture_source = CaptureSource.device

        target_types = [TargetType.face, TargetType.human, TargetType.vehicle]
        # 使用 next() 函数和生成器表达式来找到第一个匹配的 target_type
        target_type = next((t for t in target_types if t.value in capture_image_key), TargetType.nonVehicle)

        task_id = self.task_definition.taskId
        # 如果是带了 project id 则使用task id
        if self.task_config.ivmsProjectId:
            task_id = self.task_config.ivmsProjectId
        callback_msg = {
            "sn": self.task_definition.deviceSN,
            "captureSource": capture_source,
            "appId": self.task_config.appId,
            "mode": self.task_definition.mode,
            "cluster": self.task_definition.cluster,
            "alarm": self.task_definition.alarm,
            "aiType": target_type.ai_type.value,
            "imageNameType": target_type.ai_type.capture_type.value,
            "captureTime": self.capture_time,
            "uploadTime": int(time.time() * 1000),
            "channelSerial": self.task_definition.channel,
            "extraInfo": {
                "stream2captureType": capture_type,
                "taskId": task_id,
                "trackUuid": str(uuid.uuid4()),
                "imagesGroupId": self.task_definition.taskId
            },
            "images": [
                {
                    "imageName": capture_image_key,
                    "imgUrl": capture_url
                },
                {
                    "imageName": scene_image_key,
                    "imgUrl": scene_url
                },
            ],
            "location": {
                "left": location.get('x'),
                "top": location.get('y'),
                "width": location.get('width'),
                "height": location.get('height')
            },
            "score": score
        }
        # # 将第一张图片的场景图上传到S3
        # if capture_type == "offlineVideo" and ImageGroupCacheHandler.cache_cover_url(
        #         task_id=self.task_definition.taskId
        # ):
        #     await sys_ivms_service.video_snapshot_cover_callback(self.task_definition.taskId, self.task_config.deviceSN,
        #                                                          scene_url)
        #     logger.info("回调视频封面成功")
        await AiseServiceClient(
            app_id=self.task_config.systemAppInfo.id,
            app_secret=self.task_config.systemAppInfo.secret,
        ).capture_webhook(
            self.task_config.aiseEnvWebhooksUrl, callback_msg
        )
        logger.info(f"回调成功")


async def call_back(call_back_result: dict):
    '''
    c++ 回调接口用来承接，处理优选图片&进度回调的业务逻辑
    params:
        task_id: 任务id.
        capture_image_key: 回传的小图图片路径
        scene_image_key:  回传的大图的图片路径
        process: 当前的处理进度（离线视频）
        status: 任务的状态
    '''
    task_id = call_back_result.get("taskId")
    capture_image_key = call_back_result.get("captureImageKey")
    scene_image_key = call_back_result.get("sceneImageKey")
    capture_time = call_back_result.get("captureTime")
    pos = call_back_result.get("pos")
    status = call_back_result.get("status")
    location = call_back_result.get("location")
    score = call_back_result.get("score")
    task_definition = Vs2cCacheHandler.get_task_content(task_id)
    task_dict = json.loads(task_definition)
    # print(task_dict)
    if task_dict.get('skipMode'):
        return
    task_definition = TaskDefinition.parse_raw(task_definition)
    task_config = Vs2cCacheHandler.get_task_config(task_definition.deviceSN, task_definition.mainTaskId)
    task_config = TaskConfig.parse_raw(task_config)
    processor = CallBackProcessor(task_config, task_definition, capture_time)
    await processor.process_image_group(capture_image_key, scene_image_key, pos, status, location, score)


async def callback_detect_and_track(call_back_result):
    fps = 0
    task_id = call_back_result.get("taskId")
    task_definition = Vs2cCacheHandler.get_task_content(task_id)
    task_definition = json.loads(task_definition)
    callback_url = task_definition.get("callbackUrl")
    if not callback_url:
        return
    if call_back_result.get("status") == CallBackTaskStatus.TASK_ANALYSISING:
        Vs2cCacheHandler.cache_detect_and_track_result(task_id, json.dumps(call_back_result))
        logger.info(f"缓存回调数据:{task_id}")
    if call_back_result.get("status") == CallBackTaskStatus.TASK_ANALYSIS_DONE:
        d2t_result = []
        detect_result = Vs2cCacheHandler.load_detect_and_track_result(task_id)

        for item in detect_result:
            item = json.loads(item)
            fps = item["fps"]
            for d2t_item in item["result"]:
                d2t_result.append(d2t_item)

        result = {
            "timestamp": int(time.time()),
            # "token": task_definition['taskId'],
            "fps": fps,
            "result": d2t_result,
        }
        await DmsServiceClient().video_snapshot_capture_callback(callback_url, result)
        Vs2cCacheHandler.delete_detect_and_track_result(task_id)
        Vs2cCacheHandler.del_task_content(task_id)


def start_task(task_content: TaskDefinition, task_config: TaskConfig):
    detect_config = convert_detect_conf(task_config)
    # 基础云抓拍是时间间隔来抽帧 不需要按帧率抽帧，其余类型都为抽帧频率
    # c++ 解析服务已对齐帧率问题、按业务端给到的数据
    # if not task_content.mode in [AiAnalyseMode.standard_ai_mode, AiAnalyseMode.fast_video_ai_mode]:
    #     rate = 25 / rate - 1
    snapshot_conf = {"iFrameRate": int(task_content.rate)}
    if task_content.mode == AiAnalyseMode.double_analyse_ai_mode:
        snapshot_conf["iAppFrameRate"] = int(task_content.appRate)
    input_params = {
        "mode": task_content.mode,
        "videoStartTime": task_content.videoTime,
        "webhook": "http://vs2c-detect-and-track-cluster/api/task/callback",
        "sn": task_config['deviceSN'],
        "channelSerial": task_content.channel,
        "device": 0,
        "videoUrl": task_content.videoUrl,
        "workPath": "",
        "Qfactor": task_config.get("imageQfactor", 30),
        "uploadConf": {
            "sHostUrl": CntEnvConstant.S3_PRIVATE_REGEION_HOST_URL,
            "sAccessKey": CntEnvConstant.S3_PUBLIC_ACCESS_KEY,
            "sSecretKey": CntEnvConstant.S3_PUBLIC_SECRET_KEY,
            "sBucketName": CntEnvConstant.S3_PUBLIC_BUCKET_NAME,
            "sPath": f"{task_config['captureTtl']}day"
        },
        "snapshotConf": snapshot_conf,
        "detectConf": detect_config["detectConf"]
    }
    logger.info(f"[下发分析任务] 任务ID: {task_content.taskId}, 输入参数: {input_params}")
    with AnalysisClient(CntEnvConstant.ANALYSIS_SERVICE_HOST) as client:
        client.start_task(task_id=task_content.taskId, param=json.dumps(input_params))

    # oa.start_task(task_content.taskId, json.dumps(input_params))


def stop_task(task_id: str):
    logger.info(f"停止任务：{task_id}")
    with AnalysisClient(CntEnvConstant.ANALYSIS_SERVICE_HOST) as client:
        client.stop_task(task_id=task_id)


async def get_status(task_id):
    try:
        with AnalysisClient(CntEnvConstant.ANALYSIS_SERVICE_HOST) as client:
            return client.get_task_status(task_id)
    except Exception as e:
        logger.error(f"查询任务失败：{task_id}, {e}，设置为下载失败")
        return json.dumps({"status": CallBackTaskStatus.TASK_DOWNLOAD_ERR})


def start_detect_and_track_task(task_content):
    """
    {
    "videoUrl": "https://xxx",
    "fps": 7,
    "skipMode": 1,  # 1: 随机模式， 2: 平均模式
    "timestamp": 1729481010,
    "token": "xxxx",
    "callbackUrl": "https://dms.xn.sensoro.vip/dms/webhooks/v1/autotest/detectAndTrack/132/121/callback"
    }
    """
    # 1: 随机模式， 2: 平均模式
    if task_content["skipMode"] == 1:
        mode = AiAnalyseMode.detect_and_track_random
    else:
        mode = AiAnalyseMode.detect_and_track_average
    input_params = {
        "mode": mode,
        "videoStartTime": task_content["timestamp"],
        "webhook": "http://vs2c-detect-and-track-cluster/api/task/callback",
        "videoUrl": task_content['videoUrl'],
        "snapshotConf": {
            "iFrameRate": int(task_content['fps'])
        },
        "sn": "mock_sn"
    }
    logger.info(f"[下发分析任务] 任务ID: {task_content['taskId']}, 输入参数: {input_params}")
    with AnalysisClient(CntEnvConstant.ANALYSIS_SERVICE_HOST) as client:
        client.start_task(task_content['taskId'], param=json.dumps(input_params))
    # oa.start_task(task_content['taskId'], json.dumps(input_params))


def get_system_status():
    with AnalysisClient(CntEnvConstant.ANALYSIS_SERVICE_HOST) as client:
        return client.get_system_status()
