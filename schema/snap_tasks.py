from typing import Optional
from pydantic import BaseModel, Field


class StreamInfo(BaseModel):
    id: str = Field(..., description="流的唯一标识")
    source: str = Field(..., description="流来源标识")
    url: str = Field(..., description="视频流地址")
    urlRefreshCallback: Optional[str] = Field(None, description="流地址刷新回调URL（可选）")
    urlRefreshInterval: Optional[int] = Field(0, description="刷新间隔（秒，0表示不刷新）")
    urlRefreshStreamPath: Optional[str] = Field(None, description="JSON路径，用于从回调响应中提取新的流地址")


class SnapTaskRequest(BaseModel):
    id: str = Field(..., description="任务唯一标识符")
    stream: StreamInfo = Field(..., description="流信息")
    interval: float = Field(..., description="截图间隔（秒）")
    forwardReq: bool = Field(False, description="是否为转发请求（通常设为false）")
