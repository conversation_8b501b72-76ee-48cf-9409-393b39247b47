# POST /internal/v1/callbacks/snaptasks/check 接口实现文档

## 实现概述

成功实现了 `POST /internal/v1/callbacks/snaptasks/check` 接口，这是一个延时任务系统的回调接口，用于定期检查快照任务的健康状态，实现任务的自愈和故障恢复机制。

## 接口详情

### 基本信息
- **URL**: `/internal/v1/callbacks/snaptasks/check`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **功能**: 快照任务健康检查回调接口

### 请求示例

```bash
curl -X POST "http://localhost:3001/internal/v1/callbacks/snaptasks/check" \
     -H "Content-Type: application/json" \
     -d '{
       "id": "task_20241201_001",
       "clientId": "snap_job_check_task_20241201_001",
       "source": "delay_task",
       "topic": "snaptask_check",
       "callback": "http://localhost:3001/internal/v1/callbacks/snaptasks/check",
       "body": {
         "jobId": "task_20241201_001",
         "stream": {
           "id": "stream_camera_01",
           "source": "rtmp_camera",
           "url": "rtmp://*************:1935/live/camera01",
           "urlRefreshCallback": "http://api.example.com/stream/refresh",
           "urlRefreshInterval": 3600,
           "urlRefreshStreamPath": "data.streams.0"
         },
         "interval": 30.0,
         "createdTime": 1701398400000,
         "workerId": 1001
       }
     }'
```

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "status": true
  }
}
```

#### 服务器错误 (500)
```json
{
  "code": 500,
  "message": "检查任务失败: 具体错误信息",
  "data": {
    "status": false
  }
}
```

## 核心功能实现

### 1. 任务健康检查
- **任务存在性检查**: 验证任务是否在Redis缓存中存在
- **任务类型验证**: 确认是抽帧任务类型
- **状态检查**: 检查任务运行状态

### 2. 自动恢复机制
- **任务重建**: 当任务不存在时自动重新创建
- **任务重启**: 当任务失败时自动重启
- **状态修复**: 修复缺失的状态信息

### 3. 持续监控
- **延时任务创建**: 创建下一次检查的延时任务
- **监控循环**: 形成持续的健康检查循环
- **异常处理**: 即使检查失败也要确保监控继续

### 4. 异步处理
- **非阻塞响应**: 立即返回成功响应
- **后台处理**: 检查逻辑在后台异步执行
- **性能优化**: 避免阻塞延时任务系统

## 请求体字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `id` | string | 是 | 延时任务ID |
| `clientId` | string | 是 | 客户端ID |
| `source` | string | 是 | 任务来源（通常为"delay_task"） |
| `topic` | string | 否 | 主题 |
| `callback` | string | 否 | 回调URL |
| `body` | object | 是 | 任务体 |
| `body.jobId` | string | 是 | 任务ID |
| `body.stream` | object | 是 | 流信息 |
| `body.interval` | float | 是 | 截图间隔（秒） |
| `body.createdTime` | integer | 是 | 创建时间（毫秒时间戳） |
| `body.workerId` | integer | 是 | 工作节点ID |

## 检查逻辑流程

### 1. 任务存在性检查
```python
existing_task = Vs2cCacheHandler.get_task_content(job_id)
if not existing_task:
    # 任务不存在，重新创建
    await _recreate_snap_task(job_id, stream_info, interval, created_time, worker_id)
    return
```

### 2. 任务类型验证
```python
task_dict = json.loads(existing_task)
if not task_dict.get('stream'):
    # 不是抽帧任务，跳过检查
    logger.warning(f"任务 {job_id} 不是抽帧任务，跳过检查")
    return
```

### 3. 状态检查和恢复
```python
task_status_info = Vs2cCacheHandler.get_task_status(job_id)
if not task_status_info:
    # 状态信息缺失，重启任务
    await _restart_snap_task(job_id, task_dict)
    return

current_status = task_status_info.get("status")
if current_status == TaskStatus.failed:
    # 任务失败，重启任务
    await _restart_snap_task(job_id, task_dict)
    return
```

### 4. 创建下一次检查任务
```python
await _create_next_check_task(job_id, stream_info, interval, created_time, worker_id)
```

## 辅助函数实现

### 1. 重新创建任务
```python
async def _recreate_snap_task(job_id: str, stream_info: dict, interval: float, created_time: int, worker_id: int):
    """重新创建抽帧任务"""
    task_data = {
        "id": job_id,
        "stream": stream_info,
        "interval": interval,
        "forwardReq": False
    }
    
    # 缓存任务内容并启动
    Vs2cCacheHandler.set_task_content(job_id, json.dumps(task_data))
    start_snap_task(task_data)
    
    # 创建下一次检查任务
    await _create_next_check_task(job_id, stream_info, interval, created_time, worker_id)
```

### 2. 重启任务
```python
async def _restart_snap_task(job_id: str, task_dict: dict):
    """重启抽帧任务"""
    # 先停止现有任务
    stop_task(job_id)
    
    # 重新启动任务
    start_snap_task(task_dict)
    
    # 创建下一次检查任务
    stream_info = task_dict.get("stream", {})
    await _create_next_check_task(job_id, stream_info, ...)
```

### 3. 创建检查任务
```python
async def _create_next_check_task(job_id: str, stream_info: dict, interval: float, created_time: int, worker_id: int, delay: int = 60):
    """创建下一次检查的延时任务"""
    check_task_data = {
        "id": f"check_{job_id}_{int(time.time())}",
        "clientId": f"snap_job_check_{job_id}",
        "source": "delay_task",
        "callback": f"{callback_base}/internal/v1/callbacks/snaptasks/check",
        "times": 1,
        "delay": delay,
        "body": {
            "jobId": job_id,
            "stream": stream_info,
            "interval": interval,
            "createdTime": created_time,
            "workerId": worker_id
        }
    }
    
    # TODO: 调用延时任务系统API
    # await delay_task_client.create_task(check_task_data)
```

## 错误处理

### 1. 异常捕获
- 所有检查逻辑都包装在try-catch中
- 记录详细的错误日志
- 确保异常不会中断监控循环

### 2. 恢复策略
- 检查失败时延长下次检查间隔（150秒）
- 多次失败时逐步增加检查间隔
- 确保监控不会因为临时故障而停止

### 3. 日志记录
- 记录所有检查操作的详细信息
- 包含任务ID、操作类型、结果状态
- 便于问题排查和系统监控

## 测试验证

### 1. Python测试脚本
```python
def test_check_snap_task_api():
    check_data = {
        "id": "check_task_20241201_001",
        "clientId": "snap_job_check_task_20241201_001",
        "source": "delay_task",
        "body": {
            "jobId": "task_20241201_001",
            "stream": {...},
            "interval": 30.0,
            "createdTime": 1701398400000,
            "workerId": 1001
        }
    }
    
    response = requests.post(url, json=check_data, timeout=10)
    # 验证响应...
```

### 2. cURL测试
```bash
curl -X POST "http://localhost:3001/internal/v1/callbacks/snaptasks/check" \
     -H "Content-Type: application/json" \
     -d '{"id":"check_test","clientId":"snap_job_check_test",...}'
```

## 与Go版本的对比

| 功能特性 | Go版本 | Python版本 | 说明 |
|---------|--------|------------|------|
| 异步处理 | ✅ | ✅ | 都支持异步处理 |
| 任务检查 | ✅ | ✅ | 都支持完整的任务检查 |
| 自动恢复 | ✅ | ✅ | 都支持任务自动恢复 |
| 延时任务 | ✅ | ⚠️ | Python版本暂时只记录日志 |
| 跨节点支持 | ✅ | ❌ | Python版本暂未实现 |
| 错误处理 | ✅ | ✅ | 都有完善的错误处理 |

## 部署和使用

### 1. 启动服务
```bash
python api.py
```

### 2. 测试接口
```bash
# 运行Python测试
python test_snap_task.py

# 或使用cURL测试
./test_snap_task_curl.sh
```

### 3. 监控日志
- 查看检查操作日志
- 监控任务恢复情况
- 验证监控循环是否正常

## 注意事项

1. **延时任务系统**: 需要集成实际的延时任务系统
2. **监控频率**: 默认60秒检查间隔，可根据需要调整
3. **资源消耗**: 检查操作会消耗一定的系统资源
4. **网络依赖**: 依赖Redis和gRPC服务的网络连接
5. **日志管理**: 大量的检查日志需要合理管理

## 后续优化建议

1. **延时任务集成**: 集成实际的延时任务系统
2. **智能调度**: 根据任务状态动态调整检查频率
3. **批量检查**: 支持批量检查多个任务
4. **监控指标**: 添加检查成功率等监控指标
5. **告警机制**: 任务持续失败时发送告警
