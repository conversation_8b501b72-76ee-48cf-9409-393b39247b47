from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class LoadModelRequest(_message.Message):
    __slots__ = ("model_path", "config_name")
    MODEL_PATH_FIELD_NUMBER: _ClassVar[int]
    CONFIG_NAME_FIELD_NUMBER: _ClassVar[int]
    model_path: str
    config_name: str
    def __init__(self, model_path: _Optional[str] = ..., config_name: _Optional[str] = ...) -> None: ...

class Empty(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class StartTaskRequest(_message.Message):
    __slots__ = ("task_id", "param")
    TASK_ID_FIELD_NUMBER: _ClassVar[int]
    PARAM_FIELD_NUMBER: _ClassVar[int]
    task_id: str
    param: str
    def __init__(self, task_id: _Optional[str] = ..., param: _Optional[str] = ...) -> None: ...

class StopTaskRequest(_message.Message):
    __slots__ = ("task_id",)
    TASK_ID_FIELD_NUMBER: _ClassVar[int]
    task_id: str
    def __init__(self, task_id: _Optional[str] = ...) -> None: ...

class GetTaskStatusRequest(_message.Message):
    __slots__ = ("task_id",)
    TASK_ID_FIELD_NUMBER: _ClassVar[int]
    task_id: str
    def __init__(self, task_id: _Optional[str] = ...) -> None: ...

class GetTaskStatusResponse(_message.Message):
    __slots__ = ("task_status",)
    TASK_STATUS_FIELD_NUMBER: _ClassVar[int]
    task_status: str
    def __init__(self, task_status: _Optional[str] = ...) -> None: ...

class GetSystemStatusResponse(_message.Message):
    __slots__ = ("system_status",)
    SYSTEM_STATUS_FIELD_NUMBER: _ClassVar[int]
    system_status: str
    def __init__(self, system_status: _Optional[str] = ...) -> None: ...
