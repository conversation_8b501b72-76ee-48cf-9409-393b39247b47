# coding: utf-8
import time
import datetime
import logging.config

from settings import RUN_ENV, REQUEST_ID_CONTEXT

LOG_FILE = datetime.datetime.now().strftime("%Y-%m-%d") + ".log"
LOG_PATH = f"/tmp/{LOG_FILE}" if RUN_ENV in ("local", "unit_test", "performance") else f"{LOG_FILE}"


class ContextFilter:
    def __init__(self, pass_level):
        self.pass_level = pass_level

    def filter(self, record):
        record.requestid = REQUEST_ID_CONTEXT.get()
        return True


logging_config = {
    "version": 1,
    "incremental": False,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "class": "logging.Formatter",
            'format': '+ %(asctime)s.%(msecs)03dZ %(levelname)s <%(module)s> {%(requestid)s} | %(lineno)d %(message)s',
            'datefmt': '%Y-%m-%dT%H:%M:%S',
        },
    },
    "filters": {
        "filter_request_id": {
            '()': ContextFilter,
            "pass_level": logging.DEBUG
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "DEBUG",
            "formatter": "default",
            "stream": "ext://sys.stdout",
            "filters": ["filter_request_id", ]
        },

        "api": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "formatter": "default",
            "filename": LOG_PATH,
            "mode": "a+",
            "maxBytes": 1024 * 1024 * 5,  # 5 MB
            "backupCount": 20,
            "encoding": "utf8",
            "filters": ["filter_request_id", ]
        },
    },
    "root": {
        "level": "INFO",
        "handlers": ["console", "api"],
    },
}

logging.config.dictConfig(logging_config)


def beijing_time_converter(sec, what):
    timezone_offset = 0  # 默认不偏移
    if time.timezone == 0:  # 当系统为UTC时间时，偏移成北京时间
        timezone_offset = 8
    beijing_time = datetime.datetime.now() + datetime.timedelta(hours=timezone_offset)
    return beijing_time.timetuple()


logging.Formatter.converter = beijing_time_converter
logger = logging.getLogger("default")
