import grpc_client.analysis_service_pb2_grpc as analysis_service_pb2_grpc
import grpc_client.analysis_service_pb2 as analysis_service_pb2
import grpc

import grpc
from grpc_health.v1 import health_pb2_grpc
from grpc_health.v1 import health_pb2

from grpc_client.analysis_service_pb2_grpc import OfflineAnalysisServerStub
from grpc_client.analysis_service_pb2 import *


class AnalysisClient:
    def __init__(self, host: str):
        """
        初始化客户端实例。

        :param host: gRPC 服务端地址 (格式为 "host:port")。
        """
        self.host = host
        self.channel = None
        self.stub = None

    def __enter__(self):
        """
        上下文管理器进入时，初始化 gRPC 通道和存根。
        """
        self.channel = grpc.insecure_channel(self.host)
        self.stub = OfflineAnalysisServerStub(self.channel)
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        """
        上下文管理器退出时，关闭 gRPC 通道。
        """
        if self.channel:
            self.channel.close()

    def start_task(self, task_id: str, param: str) -> dict:
        """
        启动任务。

        :param task_id: 任务 ID。
        :param param: 任务参数。
        :return: 包含任务响应信息的字典。
        """

        request = StartTaskRequest(task_id=task_id, param=param)
        response = self.stub.StartTask(request,timeout=1)
        return {
            "success": True,
            "message": response,
        }

    def stop_task(self, task_id):
        request = StopTaskRequest(task_id=task_id)
        response = self.stub.StopTask(request, timeout=1)
        # print(response)

    def get_task_status(self, task_id) -> dict:
        request = GetTaskStatusRequest(task_id=task_id)
        response = self.stub.GetTaskStatus(request, timeout=1)
        # print(response.task_status)
        return response.task_status

    def get_system_status(self) -> dict:
        request = Empty()
        response = self.stub.GetSystemStatus(request, timeout=1)
        # response = GetSystemStatusResponse(system_status=response.system_status)
        # print(response.system_status)
        return response.system_status
        # return {
        #     "success": True,
        #     "message": response.,
        # }

    def check_health(self):
        health_sub = health_pb2_grpc.HealthStub(self.channel)
        request = health_pb2.HealthCheckRequest(service="")
        resp = health_sub.Check(request, timeout=1)
        if resp.status == health_pb2.HealthCheckResponse.SERVING:
            return True
        elif resp.status == health_pb2.HealthCheckResponse.NOT_SERVING:
            return False


if __name__ == '__main__':
    with AnalysisClient("10.0.0.143:12000") as client:
        client.check_health()
