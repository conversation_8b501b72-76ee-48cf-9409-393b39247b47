# coding: utf-8
""" 基础环境配置.
"""

import os
import ssl

ssl._create_default_https_context = ssl._create_unverified_context


class BaseConstant(object):
    # 注入一个环境变量的常量
    RUN_ENV = '-'

    # tornado app 相关配置
    template_path = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "templates"
    )
    static_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
    BASE_DIR = os.path.dirname(os.path.dirname(__file__))

    # 时间区域设置
    TIMEZONE = "Asia/Shanghai"

    # 系统版本
    API_VERSION = "v0.0.0"
    COOKIE_SECRET = "cookie_secret"
