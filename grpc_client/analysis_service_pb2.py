# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: analysis_service.proto
# Protobuf Python Version: 5.28.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    28,
    1,
    '',
    'analysis_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16\x61nalysis_service.proto\";\n\x10LoadModelRequest\x12\x12\n\nmodel_path\x18\x01 \x01(\t\x12\x13\n\x0b\x63onfig_name\x18\x02 \x01(\t\"\x07\n\x05\x45mpty\"2\n\x10StartTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\r\n\x05param\x18\x02 \x01(\t\"\"\n\x0fStopTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"\'\n\x14GetTaskStatusRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\",\n\x15GetTaskStatusResponse\x12\x13\n\x0btask_status\x18\x01 \x01(\t\"0\n\x17GetSystemStatusResponse\x12\x15\n\rsystem_status\x18\x01 \x01(\t2\x82\x02\n\x15OfflineAnalysisServer\x12&\n\tLoadModel\x12\x11.LoadModelRequest\x1a\x06.Empty\x12&\n\tStartTask\x12\x11.StartTaskRequest\x1a\x06.Empty\x12$\n\x08StopTask\x12\x10.StopTaskRequest\x1a\x06.Empty\x12>\n\rGetTaskStatus\x12\x15.GetTaskStatusRequest\x1a\x16.GetTaskStatusResponse\x12\x33\n\x0fGetSystemStatus\x12\x06.Empty\x1a\x18.GetSystemStatusResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'analysis_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_LOADMODELREQUEST']._serialized_start=26
  _globals['_LOADMODELREQUEST']._serialized_end=85
  _globals['_EMPTY']._serialized_start=87
  _globals['_EMPTY']._serialized_end=94
  _globals['_STARTTASKREQUEST']._serialized_start=96
  _globals['_STARTTASKREQUEST']._serialized_end=146
  _globals['_STOPTASKREQUEST']._serialized_start=148
  _globals['_STOPTASKREQUEST']._serialized_end=182
  _globals['_GETTASKSTATUSREQUEST']._serialized_start=184
  _globals['_GETTASKSTATUSREQUEST']._serialized_end=223
  _globals['_GETTASKSTATUSRESPONSE']._serialized_start=225
  _globals['_GETTASKSTATUSRESPONSE']._serialized_end=269
  _globals['_GETSYSTEMSTATUSRESPONSE']._serialized_start=271
  _globals['_GETSYSTEMSTATUSRESPONSE']._serialized_end=319
  _globals['_OFFLINEANALYSISSERVER']._serialized_start=322
  _globals['_OFFLINEANALYSISSERVER']._serialized_end=580
# @@protoc_insertion_point(module_scope)
