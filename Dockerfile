# Start FROM Nvidia PyTorch image https://ngc.nvidia.com/catalog/containers/nvidia:pytorch
#更新人体属性、人脸属性模型

#更新为r200 云抓拍
#FROM harbor.xn.sensoro.vip/library/yolo5-kunlun:v8-py3.8

FROM  ali-harbor.sensoro.com/algorithm/r200:v4
COPY ./requirements.txt ./
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple

RUN apt-get update && apt-get install -y wget &&  apt-get install -y vim #&& apt-get install gdb -y

#  && apt-get install gdb -y


RUN rm -rf /app
RUN mkdir -p /app
RUN rm -rf /app/*
WORKDIR /app
copy . /app

RUN rm -rf .git/
RUN pip install -r s3_sdk/requirements.txt -i https://mirrors.aliyun.com/pypi/simple
RUN #wget "https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/klx/models_r200.tar.gz" -O "/app/models.tar.gz" && tar -vxf "/app/models.tar.gz" -C "/app"

RUN #wget https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/klx/offline_analysis-0.0.4%2Bklx-cp38-cp38-linux_x86_64.whl && pip install "offline_analysis-0.0.4+klx-cp38-cp38-linux_x86_64.whl"
RUN #ln -s  /app/models_r200/ /app/models

RUN wget https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/klx/cluster_klx_0.0.4.tar.gz && \
    wget https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/klx/shlib.tar.gz && \
    wget https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/klx/models_r200.tar.gz && \
    tar xvf cluster_klx_0.0.4.tar.gz && \
    tar xvf shlib.tar.gz && \
    tar xvf models_r200.tar.gz &&  \
    chmod a+x cluster
ARG RUN_ENV
ENV HOME=/app
CMD ["sh", "run_server.sh"]
