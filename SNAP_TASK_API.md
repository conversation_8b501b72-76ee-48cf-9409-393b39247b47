# 抽帧任务API文档

## 接口概述

新增的抽帧任务接口 `POST /internal/v1/snaptasks` 用于创建视频流抽帧任务。该接口会对指定的视频流URL进行拉流，并根据设定的时间间隔进行抽帧处理。

## 接口详情

### 请求信息

- **URL**: `/internal/v1/snaptasks`
- **方法**: `POST`
- **Content-Type**: `application/json`

### 请求体结构

```json
{
  "id": "task_20241201_001",
  "stream": {
    "id": "stream_camera_01",
    "source": "rtmp_camera",
    "url": "rtmp://*************:1935/live/camera01",
    "urlRefreshCallback": "http://example.com/refresh",
    "urlRefreshInterval": 3600,
    "urlRefreshStreamPath": "$.data.url"
  },
  "interval": 30.0,
  "forwardReq": false
}
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `id` | string | 是 | 任务唯一标识符 |
| `stream` | object | 是 | 流信息对象 |
| `stream.id` | string | 是 | 流的唯一标识 |
| `stream.source` | string | 是 | 流来源标识 |
| `stream.url` | string | 是 | 视频流地址（支持 RTMP/RTSP/HTTP 等） |
| `stream.urlRefreshCallback` | string | 否 | 流地址刷新回调URL（可选） |
| `stream.urlRefreshInterval` | integer | 否 | 刷新间隔（秒，0表示不刷新，默认0） |
| `stream.urlRefreshStreamPath` | string | 否 | JSON路径，用于从回调响应中提取新的流地址 |
| `interval` | float | 是 | 截图间隔（秒） |
| `forwardReq` | boolean | 否 | 是否为转发请求（通常设为false，默认false） |

### 响应格式

#### 成功响应

```json
{
  "code": 200,
  "msg": "ok",
  "data": {
    "taskId": "task_20241201_001"
  }
}
```

#### 失败响应

```json
{
  "code": 500,
  "msg": "创建任务失败: 错误详情"
}
```

## 使用示例

### 基本用法

```bash
curl -X POST http://localhost:3001/internal/v1/snaptasks \
  -H "Content-Type: application/json" \
  -d '{
    "id": "task_20241201_001",
    "stream": {
      "id": "stream_camera_01",
      "source": "rtmp_camera",
      "url": "rtmp://*************:1935/live/camera01"
    },
    "interval": 30.0,
    "forwardReq": false
  }'
```

### Python示例

```python
import requests
import json

url = "http://localhost:3001/internal/v1/snaptasks"
data = {
    "id": "task_20241201_001",
    "stream": {
        "id": "stream_camera_01",
        "source": "rtmp_camera",
        "url": "rtmp://*************:1935/live/camera01"
    },
    "interval": 30.0,
    "forwardReq": False
}

response = requests.post(url, json=data)
print(response.json())
```

## 技术实现说明

### 抽帧频率计算

接口会根据 `interval` 参数计算抽帧频率：

- `interval` = 30秒 → 抽帧频率 = 1/30 ≈ 0.033 fps
- `interval` = 10秒 → 抽帧频率 = 1/10 = 0.1 fps
- `interval` = 1秒 → 抽帧频率 = 1/1 = 1 fps

抽帧频率被限制在 0.001-25 fps 范围内。

### 任务处理流程

1. 检查任务ID是否已存在，如存在则停止旧任务
2. 缓存任务内容到Redis
3. 启动抽帧任务（调用底层分析服务）
4. 更新任务状态为运行中
5. 返回成功响应

### 支持的视频流格式

- RTMP: `rtmp://example.com/live/stream`
- RTSP: `rtsp://example.com/stream`
- HTTP: `http://example.com/stream.m3u8`

## 注意事项

1. 任务ID必须唯一，重复的ID会导致旧任务被停止
2. `interval` 参数必须大于0，建议不要设置过小的值以避免系统负载过高
3. 视频流URL必须可访问，否则任务会失败
4. 抽取的帧会自动上传到S3存储，默认保存7天

## 错误处理

常见错误及解决方案：

- **任务创建失败**: 检查请求体格式是否正确
- **视频流拉取失败**: 检查URL是否可访问
- **任务已存在**: 系统会自动停止旧任务并创建新任务

## 查询抽帧任务接口

### 接口详情

- **URL**: `/internal/v1/snaptasks/{task_id}`
- **方法**: `GET`
- **参数**: `task_id` - 要查询的任务ID（路径参数）

### 请求示例

```bash
curl -X GET http://localhost:3001/internal/v1/snaptasks/task_20241201_001 \
  -H "Content-Type: application/json"
```

### 响应格式

#### 成功响应

```json
{
  "code": 200,
  "msg": "ok",
  "data": {
    "id": "task_20241201_001",
    "stream": {
      "id": "stream_camera_01",
      "source": "rtmp_camera",
      "url": "rtmp://*************:1935/live/camera01",
      "urlRefreshCallback": null,
      "urlRefreshInterval": 0,
      "urlRefreshStreamPath": null
    },
    "interval": 30.0,
    "status": 1,
    "createdTime": 1701398400.0,
    "processPod": "worker-node-01"
  }
}
```

#### 任务不存在

```json
{
  "code": 404,
  "msg": "任务不存在"
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | string | 任务唯一标识符 |
| `stream` | object | 流信息对象 |
| `stream.id` | string | 流的唯一标识 |
| `stream.source` | string | 流来源标识 |
| `stream.url` | string | 视频流地址 |
| `stream.urlRefreshCallback` | string/null | 流地址刷新回调URL |
| `stream.urlRefreshInterval` | integer | 刷新间隔（秒） |
| `stream.urlRefreshStreamPath` | string/null | JSON路径 |
| `interval` | float | 截图间隔（秒） |
| `status` | integer | 任务状态（0=待启动, 1=运行中, 2=已完成, 3=异常） |
| `createdTime` | float | 创建时间戳 |
| `processPod` | string/null | 处理节点标识 |

## 删除抽帧任务接口

### 接口详情

- **URL**: `/internal/v1/snaptasks/{task_id}`
- **方法**: `DELETE`
- **参数**: `task_id` - 要删除的任务ID（路径参数）

### 请求示例

```bash
curl -X DELETE http://localhost:3001/internal/v1/snaptasks/task_20241201_001 \
  -H "Content-Type: application/json"
```

### 响应格式

#### 成功响应

```json
{
  "code": 200,
  "msg": "ok",
  "data": {
    "status": true
  }
}
```

#### 失败响应

```json
{
  "code": 404,
  "msg": "任务不存在",
  "data": {
    "status": false
  }
}
```

### 删除功能说明

1. **任务验证**: 检查任务是否存在
2. **类型验证**: 确认是抽帧任务类型
3. **进程停止**: 停止正在运行的抽帧进程
4. **资源清理**: 删除任务内容和状态信息
5. **日志记录**: 记录删除操作

## 检查抽帧任务接口

### 接口详情

- **URL**: `/internal/v1/callbacks/snaptasks/check`
- **方法**: `POST`
- **功能**: 延时任务系统回调接口，用于定期检查快照任务健康状态

### 请求示例

```bash
curl -X POST http://localhost:3001/internal/v1/callbacks/snaptasks/check \
  -H "Content-Type: application/json" \
  -d '{
    "id": "check_task_20241201_001",
    "clientId": "snap_job_check_task_20241201_001",
    "source": "delay_task",
    "body": {
      "jobId": "task_20241201_001",
      "stream": {
        "id": "stream_camera_01",
        "source": "rtmp_camera",
        "url": "rtmp://*************:1935/live/camera01"
      },
      "interval": 30.0,
      "createdTime": 1701398400000,
      "workerId": 1001
    }
  }'
```

### 响应格式

#### 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "status": true
  }
}
```

### 功能说明

该接口实现以下核心功能：

1. **任务健康检查**: 验证任务是否正常运行
2. **自动恢复机制**: 检测到异常时自动重启或重建任务
3. **持续监控**: 创建下一次检查的延时任务，形成监控循环
4. **异步处理**: 立即返回响应，检查逻辑在后台异步执行

## 相关接口

- 创建抽帧任务: `POST /internal/v1/snaptasks`
- 查询抽帧任务: `GET /internal/v1/snaptasks/{task_id}`
- 删除抽帧任务: `DELETE /internal/v1/snaptasks/{task_id}`
- 检查抽帧任务: `POST /internal/v1/callbacks/snaptasks/check`
- 停止任务: `POST /api/task/stop`
- 查询任务状态: `POST /api/task/status/{task_id}`
- 系统状态: `POST /api/node/sys`
