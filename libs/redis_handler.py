import datetime
import json
from typing import Dict, List, Any

from redis import Red<PERSON>, Sentinel, StrictRedis

from settings import CntEnvConstant


def init_redis_pool() -> Redis:
    if CntEnvConstant.REDIS_TESTING:

        redis_con = StrictRedis(host=CntEnvConstant.BACKEND_REDIS_HOST, port=CntEnvConstant.BACKEND_REDIS_PORT,
                                db=CntEnvConstant.BACKEND_REDIS_DB, password=CntEnvConstant.BACKEND_REDIS_PASS)
    else:
        redis_con = StrictRedis(host=CntEnvConstant.BACKEND_REDIS_HOST, port=CntEnvConstant.BACKEND_REDIS_PORT,
                                db=CntEnvConstant.BACKEND_REDIS_DB, password=CntEnvConstant.BACKEND_REDIS_PASS)
        # # 正式环境走哨兵
        # sentinel = Sentinel([(CntEnvConstant.BACKEND_REDIS_HOST, CntEnvConstant.BACKEND_REDIS_PORT)], socket_timeout=2)
        # redis_con = sentinel.master_for(
        #     "mymaster", socket_timeout=1, password=CntEnvConstant.BACKEND_REDIS_PASS, db=CntEnvConstant.BACKEND_REDIS_DB
        # )
    return redis_con


class RedisKey:
    # 封面图cache
    VIDEO_COVER_CACHE = "videoCoverCache:{taskId}"
    # 任务的config缓存，类型为hash
    CONF_KEY = "stream2captureConf"
    # 具体任务的配置，类型为hash
    TASK_KEY = "stream2captureTask"
    # 任务的状态，类型为kv 过期时间为三个抽帧时间
    TASK_STATUS = "stream2captureTaskStatus:{taskId}"
    # 任务和pod的关联关系，确定任务在具体的pod中 类型是 hash
    TASK_TO_POD = "stream2captureTask2Pod"
    # 分布式锁，类型为kv，过期时间为30s，使用完后会自动过期
    TIMED_LOCK = "stream2captureTaskLock"
    # 当前pod 运行的任务个数
    POD_TASK_COUNT = "stream2capturePodTaskCount:{POD_IP}"
    # 当前pod 运行的任务个数前缀
    POD_TASK_COUNT_PRE = "stream2capturePodTaskCount:"
    # 检测&追踪结果存储，类型为stream
    DETECT_AND_TRACK_RESULT = "stream2captureDetectAndTrackResult:{taskId}"
    USER_NODE_KEY = f"stream2captureUserNode:{CntEnvConstant.SERVICE_BRANCH}"
    POD_SYS_INFO = "stream2capturePodSystemInfo"
    POD_Ip2Name = "stream2captureIp2Name"
    TASK_CONFIG_FIELD = "{sn}{main_task_id}"
    RECORD_TASK_GROUP = "stream2captureTaskGroup:{mianTaskId}"

    @property
    def RESOURCE_PRESENT_DATE(self):
        return f"stream2captureResource:{datetime.datetime.today().strftime('%Y-%m-%d')}"


class RedisHandler:
    def __init__(self, redis: Redis = init_redis_pool()) -> None:
        self._redis = redis

    def set(self, name: str, value: Any, ex: int = None, nx: bool = False):
        return self._redis.set(name, value, ex=ex, nx=nx)

    def get(self, name: str) -> str:
        return self._redis.get(name)  # type: ignore

    def delete(self, *name: str) -> int:
        return self._redis.delete(*name)

    def incr(self, name: str, count: int = 1) -> int:
        return self._redis.incr(name, count)

    def xadd(self, name: str, fields: Dict) -> None:
        self._redis.xadd(name, fields)

    def batch_xadd(self, name: str, payloads: List[Dict]) -> None:
        pipe = self._redis.pipeline()
        for payload in payloads:
            pipe.xadd(name, payload)
        pipe.execute()

    def hset(self, name: str, key: str, value: Dict) -> None:
        self._redis.hset(name=name, key=key, value=json.dumps(value))

    def hdel(self, name: str, *keys: str) -> None:
        self._redis.hdel(name, *keys)

    def hexists(self, name: str, key: str) -> bool:
        return self._redis.hexists(name, key)

    def hmset(self, name: str, mapping: Dict) -> None:
        self._redis.hset(name=name, mapping={key: json.dumps(value) for key, value in mapping.items()})

    def hgetall(self, name: str) -> Dict:
        return {item: json.loads(value) for item, value in self._redis.hgetall(name=name).items()}

    def keys(self, key: str):
        return self._redis.keys(key)


class ImageGroupCacheHandler:
    redis = RedisHandler()

    @classmethod
    def cache_cover_url(cls, task_id: str) -> bool:
        redis_key = RedisKey.VIDEO_COVER_CACHE.format(taskId=task_id)
        return cls.redis.set(redis_key, value=1, ex=60 * 60, nx=True)


class Vs2cCacheHandler:
    redis = init_redis_pool()

    @classmethod
    def cache_pod_task_count(cls, pod_ip: str, count: int, is_init=False):
        redis_key = RedisKey.POD_TASK_COUNT.format(POD_IP=pod_ip)
        if is_init:
            return cls.redis.set(redis_key, value=count, ex=60)
        else:
            current_count = cls.redis.get(redis_key)
            if not current_count:
                update_count = count
            else:
                update_count = count + int(current_count)
            return cls.redis.set(redis_key, value=update_count, ex=60)

    @classmethod
    def get_pod_task_count(cls, pod_ip: str):
        redis_key = RedisKey.POD_TASK_COUNT.format(POD_IP=pod_ip)
        return cls.redis.get(redis_key)

    @classmethod
    def cache_task_content(cls, task_id, task_content):
        cls.redis.hset(RedisKey.TASK_KEY, task_id, task_content)

    @classmethod
    def get_task_content(cls, task_id):
        return cls.redis.hget(RedisKey.TASK_KEY, task_id)

    @classmethod
    def load_task_content(cls):
        return cls.redis.hgetall(RedisKey.TASK_KEY)

    @classmethod
    def load_task_keys(cls):
        return cls.redis.hkeys(RedisKey.TASK_KEY)

    @classmethod
    def del_task_content(cls, task_id):
        return cls.redis.hdel(RedisKey.TASK_KEY, task_id)

    @classmethod
    def cache_task_config(cls, sn, task_config, main_task_id):
        task_field = RedisKey.TASK_CONFIG_FIELD.format(sn=sn, main_task_id=main_task_id)
        cls.redis.hset(RedisKey.CONF_KEY, task_field, task_config)

    @classmethod
    def get_task_config(cls, sn, main_task_id):
        task_field = RedisKey.TASK_CONFIG_FIELD.format(sn=sn, main_task_id=main_task_id)
        return cls.redis.hget(RedisKey.CONF_KEY, task_field)

    @classmethod
    def del_task_config(cls, sn, main_task_id):
        task_field = RedisKey.TASK_CONFIG_FIELD.format(sn=sn, main_task_id=main_task_id)
        return cls.redis.hdel(RedisKey.CONF_KEY, task_field)

    @classmethod
    def cache_task_status(cls, task_id, task_status):
        redis_key = RedisKey.TASK_STATUS.format(taskId=task_id)
        return cls.redis.set(redis_key, value=task_status)

    @classmethod
    def del_task_status(cls, task_id):
        redis_key = RedisKey.TASK_STATUS.format(taskId=task_id)
        return cls.redis.delete(redis_key)

    @classmethod
    def get_task_status(cls, task_id):
        redis_key = RedisKey.TASK_STATUS.format(taskId=task_id)
        result = cls.redis.get(redis_key)
        if result:
            result = json.loads(result)
        return result

    @classmethod
    def cache_detect_and_track_result(cls, task_id, result):
        redis_key = RedisKey.DETECT_AND_TRACK_RESULT.format(taskId=task_id)
        cls.redis.rpush(redis_key, result)
        cls.redis.expire(redis_key, 60 * 60)  # 30天过期

    @classmethod
    def delete_detect_and_track_result(cls, task_id):
        redis_key = RedisKey.DETECT_AND_TRACK_RESULT.format(taskId=task_id)
        cls.redis.delete(redis_key)

    @classmethod
    def load_detect_and_track_result(cls, task_id):
        redis_key = RedisKey.DETECT_AND_TRACK_RESULT.format(taskId=task_id)
        return cls.redis.lrange(redis_key, 0, -1)

    @classmethod
    def cache_resource_used_by_date(cls, time_str, used):
        redis_key = RedisKey().RESOURCE_PRESENT_DATE
        cls.redis.hset(redis_key, time_str, used)
        cls.redis.expire(redis_key, 15 * 60 * 60 * 24)  # 15天过期

    @classmethod
    def load_resource_used_by_date(cls, ):
        redis_key = RedisKey().RESOURCE_PRESENT_DATE
        return cls.redis.hgetall(redis_key)

    @classmethod
    def add_sub_record_task(cls, main_task_id, sub_task_id):
        redis_key = RedisKey.RECORD_TASK_GROUP.format(mianTaskId=main_task_id)
        return cls.redis.sadd(redis_key, sub_task_id)

    @classmethod
    def get_sub_record_task(cls, main_task_id):
        redis_key = RedisKey.RECORD_TASK_GROUP.format(mianTaskId=main_task_id)
        task_ids = cls.redis.smembers(redis_key)
        return {id.decode('utf-8') for id in task_ids}

    @classmethod
    def del_sub_record_task(cls, main_task_id):
        redis_key = RedisKey.RECORD_TASK_GROUP.format(mianTaskId=main_task_id)
        cls.redis.delete(redis_key)
        r


if __name__ == "__main__":
    r = init_redis_pool().hgetall("stream2captureTask")
    count = 0
    count_h = 0
    with open("result.csv", "w") as f:
        # RedisHandler().delete(*r)
        for i in r:
            # print(i)
            result = init_redis_pool().hget("stream2captureTask", i)
            result = json.loads(result)
            if result['mode'] == 0:
                count += 1
            else:
                count_h += 1

            # print(f"{result['taskId']},{result['mode']}", file=f)
            # if '-' in result['taskId']:
            #     init_redis_pool().hdel("stream2captureTask", result['taskId'])
