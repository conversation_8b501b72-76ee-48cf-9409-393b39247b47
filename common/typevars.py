from enum import Enum


class ImageCaptureType(int, Enum):
    """图片抓拍类型"""

    FACE = 1  # 人脸
    FACE_CAPTURE = 2
    FACE_SCENE = 3
    BODY = 4  # 人体
    BODY_CAPTURE = 5
    BODY_SCENE = 6
    MotorVehicle = 7  # 机动车
    MotorVehicle_CAPTURE = 8
    MotorVehicle_SCENE = 9
    NonMotorVehicle = 10  # 非机动车
    NonMotorVehicle_CAPTURE = 11
    NonMotorVehicle_SCENE = 12
    BOAT = 13  # 船只
    BOAT_CAPTURE = 14
    BOAT_SCENE = 15

    @property
    def capture_type(self):
        return {
            ImageCaptureType.FACE: ImageCaptureType.FACE_CAPTURE,
            ImageCaptureType.BODY: ImageCaptureType.BODY_CAPTURE,
            ImageCaptureType.MotorVehicle: ImageCaptureType.MotorVehicle_CAPTURE,
            ImageCaptureType.NonMotorVehicle: ImageCaptureType.NonMotorVehicle_CAPTURE,
            ImageCaptureType.BOAT: ImageCaptureType.BOAT_CAPTURE,
        }[self]

    @property
    def scene_type(self):
        return {
            ImageCaptureType.FACE: ImageCaptureType.FACE_SCENE,
            ImageCaptureType.BODY: ImageCaptureType.BODY_SCENE,
            ImageCaptureType.MotorVehicle: ImageCaptureType.MotorVehicle_SCENE,
            ImageCaptureType.NonMotorVehicle: ImageCaptureType.NonMotorVehicle_SCENE,
            ImageCaptureType.BOAT: ImageCaptureType.BOAT_SCENE,
        }[self]


class TargetType(str, Enum):
    face = "RL"
    human = "RT"
    vehicle = "MO"
    nonVehicle = "NM"

    @property
    def ai_type(self) -> ImageCaptureType:
        return {
            TargetType.face: ImageCaptureType.FACE,
            TargetType.human: ImageCaptureType.BODY,
            TargetType.vehicle: ImageCaptureType.MotorVehicle,
            TargetType.nonVehicle: ImageCaptureType.NonMotorVehicle,
        }[self]

    @property
    def conf_key(self) -> str:
        return {
            TargetType.face: "face",
            TargetType.human: "body",
            TargetType.vehicle: "vehicle",
            TargetType.nonVehicle: "nonVehicle",
        }[self]


class DeviceType(str, Enum):
    """设备类型"""

    gb28181 = "gb28181"
    rtmp = "rtmp"


class AiAnalyseMode(int, Enum):
    """
    AI分析模式
    0: 基础云AI分析
    1：高级实时流分析
    2：高级录像二次分析
    3: 视频极速分析
    4: 智能调度
    5: lins 离线分析
    6: 基础云&高级云

    14:检测跟踪任务(随机抽帧)
    15:检测跟踪任务(平均抽帧)
    """
    standard_ai_mode = 0
    advanced_live_ai_mode = 1
    advanced_reanalyse_ai_mode = 2
    fast_video_ai_mode = 3
    smart_schedule_ai_mode = 4
    fast_video_ai_mode_aicity = 5
    double_analyse_ai_mode = 6



    detect_and_track_random = 14
    detect_and_track_average = 15
    detect_and_track_best = 16


class CaptureSource(int, Enum):
    device = 1  # 设备
    cloud = 2  # 基础云
    advance_cloud = 3  # 高级云


class TaskPlatform(int, Enum):
    """
    AI分析模式
    0: 基础云AI分析
    1：高级实时流分析
    """
    r200 = 0
    ax650 = 1


class TaskStatus(int, Enum):
    running = 1
    complete = 2
    failed = 3
    pending = 0


class CallBackTaskType(str, Enum):
    task_stats = "on_vs2c_task_status"
    task_process = "on_vs2c_task_process"
    task_result = "on_vs2c_task_result"


class CallBackTaskStatus(int, Enum):
    TASK_NOT_FINDED = 0  # 任务不存在
    TASK_INITING = 1  # 任务初始化中
    TASK_DOWNLOADING = 2  # 文件下载中
    TASK_DOWNLOAD_ERR = 3  # 文件下载失败
    TASK_STREAM_PULL_ERR = 4  # 视频文件不支持或实时流拉取失败
    TASK_ANALYSISING = 5  # 视频分析中
    TASK_DECODE_ERR = 6  # 视频解码失败
    TASK_ANALYSIS_DONE = 7  # 视频分析完成
