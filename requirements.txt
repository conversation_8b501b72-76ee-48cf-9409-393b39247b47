# pip install -r requirements.txt

# base ----------------------------------------
#matplotlib
#numpy

#Pillow
PyYAML
#scipy

#tqdm

#seaborn
#pandas


# extras --------------------------------------
#thop  # FLOPS computation
#pycocotools  # COCO mAP

#bcrypt
#arrow
#pytz
flake8
#docopt
#python-dateutil
#marshmallow
#prometheus_client
#psutil

#setuptools
#future
pydantic==1.8.1
#aiofiles

fastapi
uvicorn
#typing_extensions
retrying_async
aiohttp
locust

#loguru==0.6.0
#httpx==0.23.1
#cython_bbox
#lap
redis==4.3.4
async-property==0.2.1

grpcio==1.68.1
grpcio-health-checking==1.68.1

aiofiles==0.6.0
aioboto3==11.3.1
#smart_open[s3]==6.4.0
#pycrypto==2.6.1
apscheduler
filelock
httpx