# coding: utf-8

import hashlib
import hmac
import json
import time
import uuid
from logger import logger
import aiohttp

from common.utils import retry
from settings import CntEnvConstant, REQUEST_ID_CONTEXT


class AiseServiceClient:
    def __init__(self, app_id=CntEnvConstant.SYS_ID, app_secret=CntEnvConstant.SYS_SECRET):
        self.url_map = dict(
            CAPTURE_WEBHOOK_URL="/webhooks/v1/captureMsg",  # 数据回流
            OFFLINE_VIDEO_PROGRESS_URL="/webhooks/v1/offlineVideoProgress",  # 离线视频分析进度回调
        )
        self.app_id = app_id
        self.app_secret = app_secret

        self.headers = {"Content-Type": "application/json"}  # 默认请求头

    def _generate_hmac_sha256(self, x_access_nonce, request_url, request_method, request_body):
        """生成signature"""
        message = x_access_nonce + request_method + request_url
        if request_body is not None:
            message += request_body

        key = self.app_secret.encode("utf-8")
        message = message.encode("utf-8")
        signature = hmac.new(key, message, digestmod=hashlib.sha256).hexdigest()
        return signature

    def _make_request_headers(self, request_url, request_method, request_body):
        """构造鉴权请求头"""
        x_access_nonce = str(int(time.time() * 1000))

        if request_body is None and not request_method == "GET":
            request_body = json.dumps({})

        signature = self._generate_hmac_sha256(
            x_access_nonce=x_access_nonce,
            request_url=request_url,
            request_method=request_method,
            request_body=request_body,
        )

        trace_id = REQUEST_ID_CONTEXT.get() if REQUEST_ID_CONTEXT.get() != "-" else str(uuid.uuid4())
        headers = {
            "X-ACCESS-ID": self.app_id,
            "X-ACCESS-NONCE": x_access_nonce,
            "X-ACCESS-SIGNATURE": signature,
            "X-TRACE-ID": trace_id,
        }
        self.headers.update(headers)  # 将鉴权信息更新至请求头

    @retry(times=2, delay=0.2)
    async def smart_request(self, url, body, method, headers) -> dict:
        async with aiohttp.ClientSession() as session:
            async with session.request(method=method, url=url, data=body, headers=headers) as resp:
                assert resp.status == 200, f"请求失败, {resp.status}, {resp.reason}"
                return await resp.json()

    async def base_request(self, method, url, body=None):
        if isinstance(body, (dict, list)):
            body = json.dumps(body)

        self._make_request_headers(request_url=url, request_method=method, request_body=body)
        return await self.smart_request(url=url, body=body, method=method, headers=self.headers)

    async def capture_webhook(self, webhook_url: str, webhook_msg: dict):
        logger.info(f"aise 回调抓拍 =========回调地址：{webhook_url}========== appid为: {self.app_id} secret为: {self.app_secret}")
        response_body = await self.base_request(
            method="POST", url=webhook_url, body=webhook_msg
        )
        return response_body.get("data", {})

    async def offline_video_progress_webhook(self, webhook_url: str, webhook_msg: dict):
        logger.info(f"aise 回调进度 =========回调地址：{webhook_url}========== appid为: {self.app_id} secret为: {self.app_secret}")
        response_body = await self.base_request(
            method="POST", url=webhook_url, body=webhook_msg
        )
        return response_body.get("data", {})

sys_aise_service = AiseServiceClient(CntEnvConstant.SYS_ID, CntEnvConstant.SYS_SECRET)
