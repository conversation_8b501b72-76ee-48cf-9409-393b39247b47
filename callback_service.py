# coding: utf-8
import asyncio
import json
import hashlib
import logging

from settings import CntEnvConstant
from utils.http_client import AioHTTPClient


class CallbackService(object):
    def __init__(self, ):
        self.host = CntEnvConstant.FFMPEG_SERVICE_HOST

        self.ffmpeg_url = self.host + "v1/streams/detect/notifications"
        self.paint_video_url = self.host + ":3002/imageGroup"
        self.capture_mock_url = self.host + ":3003/imageGroup"

        self.headers = {
            "Content-Type": 'application/json',
            "From-Env": CntEnvConstant.RUN_ENV,
        }

    async def base_request(self, api_url, payload, timeout: int = 3) -> tuple:
        async with AioHTTPClient(timeout=timeout) as session:
            async with session.post(
                    url=api_url,
                    headers=self.headers,
                    data=json.dumps(payload)
            ) as resp:
                logging.debug(f"POST {api_url} {resp.status} {json.loads(await resp.read())}")
                return resp.status, await resp.json()

    async def capture_mock(self, payload, timeout: int = 3):
        """
            回调抓拍模拟
        """
        status_code, response_data = await self.base_request(
            api_url=self.capture_mock_url,
            payload=payload,
            timeout=timeout
        )
        assert status_code == 200
        return response_data

    async def paint_video(self, payload, timeout: int = 3):
        """
            回调 画图回调
        """
        status_code, response_data = await self.base_request(
            api_url=self.paint_video_url,
            payload=payload,
            timeout=timeout
        )
        assert status_code == 200
        return response_data

    async def ffmpeg(self, payload, timeout: int = 3):
        """
            回调 ffmpge 通知是否进行图片保存
        """
        status_code, response_data = await self.base_request(
            api_url=self.ffmpeg_url,
            payload=payload,
            timeout=timeout
        )
        assert status_code == 200
        return response_data


callback_service = CallbackService()
