import contextlib

import boto3
from smart_open import open
from settings import CntEnvConstant


class SyncBoto:
    def __init__(self, bucket_name, region_name, public_endpoint_url, private_endpoint_url, access_key, secret_key, sign_version, sign_style):
        self.bucket_name = bucket_name
        self.region_name = region_name
        self.public_endpoint_url = public_endpoint_url
        self.private_endpoint_url = private_endpoint_url
        self.access_key = access_key
        self.secret_key = secret_key
        self.sign_version = sign_version
        self.sign_style = "virtual" if sign_style == "host" else "path"

    def get_client(self):
        session = boto3.session.Session()
        return session.client(
            service_name="s3",
            region_name=self.region_name,
            endpoint_url=self.private_endpoint_url,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            config=boto3.session.Config(signature_version=self.sign_version, s3={"addressing_style": self.sign_style}),
        )

    @contextlib.contextmanager
    def get_upload_fp(self, oss_key, part_size: int = 40 * 1024 * 1024):
        with open(
            f"s3://{self.bucket_name}/{oss_key}",
            "wb",
            transport_params={"client": self.get_client(), "min_part_size": part_size},
        ) as fout:
            yield fout


default_sync_boto = SyncBoto(
    bucket_name=CntEnvConstant.S3_PUBLIC_BUCKET_NAME,
    region_name=CntEnvConstant.S3_PUBLIC_REGEION,
    public_endpoint_url=CntEnvConstant.S3_PUBLIC_REGEION_HOST_URL,
    private_endpoint_url=CntEnvConstant.S3_PRIVATE_REGEION_HOST_URL,
    access_key=CntEnvConstant.S3_PUBLIC_ACCESS_KEY,
    secret_key=CntEnvConstant.S3_PUBLIC_SECRET_KEY,
    sign_version=CntEnvConstant.S3_PUBLIC_SIGN_VERSION,
    sign_style=CntEnvConstant.S3_PUBLIC_SIGN_STYLE,
)
