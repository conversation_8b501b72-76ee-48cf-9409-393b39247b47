#python3 /app/download_ai_model.py
#ln -s -f /lib/x86_64-linux-gnu/libffi.so.7.1.0 /root/miniconda/envs/python38_torch201_cuda/lib/libffi.so.7
export  AMS_VSC_GET_URL="https://vsc-get-wx.xn.sensoro.vip"
#nohup python3 /app/timed.py > apscheduler.log 2>&1 &
source set_r200_env.sh
#wget https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/klx/soft_reset && chmod +x  soft_reset &&  ./soft_reset 0
#echo -e "run\nbt\nquit" > gdb_command.txt
#gdb -batch -x gdb_command.txt --args python3 api.py
#python3 api.py
uvicorn api:app --host "0.0.0.0" --port 3001 --workers 1 --limit-concurrency 1256 --reload