#!/bin/bash

# 抽帧任务API测试脚本
# 使用cURL测试创建和删除抽帧任务接口

BASE_URL="http://localhost:3001"
TASK_ID="task_curl_test_$(date +%s)"

echo "=== 抽帧任务API cURL测试 ==="
echo "基础URL: $BASE_URL"
echo "任务ID: $TASK_ID"
echo ""

# 测试数据
TEST_DATA='{
  "id": "'$TASK_ID'",
  "stream": {
    "id": "stream_curl_test",
    "source": "rtmp_camera",
    "url": "rtmp://*************:1935/live/camera01"
  },
  "interval": 30.0,
  "forwardReq": false
}'

echo "1. 测试创建抽帧任务"
echo "请求数据:"
echo "$TEST_DATA" | jq .
echo ""

echo "发送创建请求..."
CREATE_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST "$BASE_URL/internal/v1/snaptasks" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA")

HTTP_CODE=$(echo "$CREATE_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$CREATE_RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容:"
echo "$RESPONSE_BODY" | jq .
echo ""

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 创建任务成功"
    
    echo ""
    echo "2. 等待3秒..."
    sleep 3
    
    echo ""
    echo "3. 测试删除抽帧任务"
    echo "任务ID: $TASK_ID"
    echo ""
    
    echo "发送删除请求..."
    DELETE_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X DELETE "$BASE_URL/internal/v1/snaptasks/$TASK_ID" \
      -H "Content-Type: application/json")
    
    DELETE_HTTP_CODE=$(echo "$DELETE_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
    DELETE_RESPONSE_BODY=$(echo "$DELETE_RESPONSE" | sed '/HTTP_CODE:/d')
    
    echo "HTTP状态码: $DELETE_HTTP_CODE"
    echo "响应内容:"
    echo "$DELETE_RESPONSE_BODY" | jq .
    echo ""
    
    if [ "$DELETE_HTTP_CODE" = "200" ]; then
        echo "✅ 删除任务成功"
        echo "✅ 完整流程测试通过"
    else
        echo "❌ 删除任务失败"
    fi
else
    echo "❌ 创建任务失败，跳过删除测试"
fi

echo ""
echo "=== 测试其他场景 ==="

echo ""
echo "4. 测试删除不存在的任务"
NONEXISTENT_TASK_ID="nonexistent_task_$(date +%s)"
echo "任务ID: $NONEXISTENT_TASK_ID"

DELETE_NONEXISTENT_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X DELETE "$BASE_URL/internal/v1/snaptasks/$NONEXISTENT_TASK_ID" \
  -H "Content-Type: application/json")

DELETE_NONEXISTENT_HTTP_CODE=$(echo "$DELETE_NONEXISTENT_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
DELETE_NONEXISTENT_RESPONSE_BODY=$(echo "$DELETE_NONEXISTENT_RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $DELETE_NONEXISTENT_HTTP_CODE"
echo "响应内容:"
echo "$DELETE_NONEXISTENT_RESPONSE_BODY" | jq .

if [ "$DELETE_NONEXISTENT_HTTP_CODE" = "404" ]; then
    echo "✅ 正确返回404错误"
else
    echo "❌ 未正确处理不存在的任务"
fi

echo ""
echo "=== 测试完成 ==="

# 使用说明
echo ""
echo "使用说明:"
echo "1. 确保服务运行在 $BASE_URL"
echo "2. 确保安装了 jq 工具用于格式化JSON输出"
echo "3. 如果没有 jq，可以移除 '| jq .' 部分"
echo ""
echo "手动测试命令:"
echo ""
echo "创建任务:"
echo "curl -X POST $BASE_URL/internal/v1/snaptasks \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"id\":\"test_task\",\"stream\":{\"id\":\"test_stream\",\"source\":\"test\",\"url\":\"rtmp://test.com/live\"},\"interval\":30.0,\"forwardReq\":false}'"
echo ""
echo "删除任务:"
echo "curl -X DELETE $BASE_URL/internal/v1/snaptasks/test_task \\"
echo "  -H \"Content-Type: application/json\""
