import asyncio
import json
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.interval import IntervalTrigger

from ams_sdk.client import IvmsServiceClient
from common.typevars import TaskStatus, CallBackTaskStatus, AiAnalyseMode, TaskPlatform
from libs.redis_handler import init_redis_pool, RedisKey, Vs2cCacheHandler
from logger import logger
from settings import CntEnvConstant

IMAGE_DIRECTORY = "/pic"
EXPIRATION_TIME_HOURS = 1

redis = init_redis_pool()

pod_namespace = os.getenv("POD_NAMESPACE")

import httpx

http_client = httpx.Client(timeout=2)


def choice_pod():
    # 定义部分前缀

    keys_and_values = {}
    cursor = 0
    while True:
        # 使用 SCAN 查找以指定前缀开头的键
        cursor, found_keys = redis.scan(cursor=cursor, match=f"{RedisKey.POD_TASK_COUNT_PRE}*", count=100)

        # 遍历找到的键，并获取每个键的值
        for key in found_keys:
            value = redis.get(key)  # 如果是字符串类型的键值对
            keys_and_values[key.decode('utf-8')] = value.decode('utf-8') if value else None

        if cursor == 0:
            break

    # return keys_and_values
    # 找到负载最小的pod ip
    min_pod_ip, min_value = min(keys_and_values.items(), key=lambda item: item[1])
    # logger.info(f"找到最小负载的pod ip为:{min_pod_ip},负载为:{min_value}")
    return min_pod_ip.split(":")[-1]


def call_back_ivms(task_dict):
    task_config = Vs2cCacheHandler.get_task_config(task_dict['deviceSN'], task_dict["mainTaskId"])
    task_config = json.loads(task_config)
    sys_app = task_config['systemAppInfo']
    asyncio.run(IvmsServiceClient(
        app_id=sys_app['id'],
        app_secret=sys_app['secret'],
    ).video_snapshot_status_callback(task_dict['snapshotWebhookUrl'],
                                     task_dict['mainTaskId'],
                                     "abnormal"))


def check_detect_service_status():
    lock_value = str(time.time())
    expire_time = 60 * 3  # 锁的过期时间（秒）
    # 使用 SET 命令同时设置过期时间和获取锁
    lock_acquired = redis.set(
        RedisKey.TIMED_LOCK, lock_value, nx=True, ex=expire_time
    )

    if lock_acquired:
        try:
            # 获取全部的task
            all_task_key = Vs2cCacheHandler.load_task_keys()

            # 创建线程池
            with ThreadPoolExecutor(max_workers=16) as executor:
                futures = []

                for task_id in all_task_key:
                    # 提交任务到线程池
                    future = executor.submit(process_single_task, task_id)
                    futures.append(future)

                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"任务处理失败: {e}")

        finally:
            logger.info("任务完成")
    else:
        logger.info("锁已存在，无法获取锁")


def process_single_task(task_id):
    task_json = Vs2cCacheHandler.get_task_content(task_id.decode('utf-8'))
    task_dict = json.loads(task_json)
    """处理单个任务的函数"""
    if task_dict.get("taskPlatform") == TaskPlatform.ax650:
        return

    task_id = task_dict["taskId"]

    if not 'mode' in task_dict:
        print(task_dict)
        return

    if task_dict['status'] == TaskStatus.running:
        process_running_task(task_dict, task_id)
    elif task_dict['status'] == TaskStatus.pending:
        process_pending_task(task_dict, task_id)


def process_running_task(task_dict, task_id):
    """处理运行中的任务"""
    task_status_dict = Vs2cCacheHandler.get_task_status(task_id)
    if not task_status_dict:
        task_dict["status"] = TaskStatus.pending
        Vs2cCacheHandler.cache_task_content(task_id, json.dumps(task_dict))
        return

    task_status = task_status_dict["status"]
    task_running_ip = task_status_dict['pod_ip']
    pod = Vs2cCacheHandler.get_pod_task_count(task_running_ip)

    if task_status == TaskStatus.failed:
        handle_failed_task(task_dict, pod, task_running_ip)
    elif task_status == TaskStatus.complete:
        handle_complete_task(task_dict)
    elif task_status == TaskStatus.running:
        handle_running_task(task_dict, pod, task_running_ip, task_id)


def handle_failed_task(task_dict, pod, task_running_ip):
    """处理失败的任务"""
    if task_dict["mode"] in [2, 3, 5]:
        Vs2cCacheHandler.del_task_content(task_dict['taskId'])
        logger.info(f"{task_dict['taskId']},mode:{task_dict['mode']},直接删除任务")
        return
    try:
        if pod:
            pass
        logger.info(f"{task_dict['taskId']} 任务执行失败，回调ivms重新下发任务")
        call_back_ivms(task_dict)
    except Exception as e:
        logger.info(f"{task_dict['taskId']} 回调ivms重新下发任务失败")


def handle_complete_task(task_dict):
    """处理完成的任务"""
    logger.info(f"{task_dict['taskId']} 任务执行完成")
    Vs2cCacheHandler.del_task_content(task_dict['taskId'])
    Vs2cCacheHandler.del_task_status(task_dict['taskId'])


def handle_running_task(task_dict, pod, task_running_ip, task_id):
    """处理正在运行的任务"""
    if pod:
        try:
            resp = http_client.post(
                f"http://{task_running_ip}:3001/api/task/status/{task_id}",
                json=task_dict,
                timeout=2
            )
            if resp.status_code == 200:
                resp_dict = resp.json()
                if resp_dict['data']['status'] in [
                    CallBackTaskStatus.TASK_DECODE_ERR,
                    CallBackTaskStatus.TASK_DOWNLOAD_ERR,
                    CallBackTaskStatus.TASK_STREAM_PULL_ERR,
                    CallBackTaskStatus.TASK_NOT_FINDED
                ]:
                    call_back_ivms(task_dict)
                logger.info(f"{task_dict['taskId']} 任务执行正常，任务状态为：{resp_dict['data']['status']}")
            else:
                logger.info("查询任务失败")
        except Exception as e:
            logger.error(f"查询任务失败，失败原因为:{e} ", exc_info=True)
    else:
        logger.info(f"pod 已经丢失，{task_dict['taskId']} 重新下发任务")
        task_dict["status"] = TaskStatus.pending
        Vs2cCacheHandler.cache_task_content(task_id, json.dumps(task_dict))


def process_pending_task(task_dict, task_id):
    """处理待处理的任务"""
    task_ip = choice_pod()
    logger.info(f"下发的任务为:{task_id},目标pod为:{task_ip}")
    try:
        http_client.post(f"http://{task_ip}:3001/api/task/video/process", json=task_dict)
    except Exception as e:
        logger.info(f"pod 任务下发超时：{task_ip}")


def set_pod_ex_time():
    logger.info(f"保活当前pod: {CntEnvConstant.POD_IP}")
    redis.expire(RedisKey.POD_TASK_COUNT.format(POD_IP=CntEnvConstant.POD_IP), 10)
    resp = http_client.post(f"http://0.0.0.0:3001/api/node/sys")
    sys_status = resp.json()
    if sys_status:
        sys_status_dict = sys_status.get("data", {})
        sys_status_format = {"cpu": sys_status_dict.get("cpu"), "cpuMem": sys_status_dict.get("cpu_mem"),
                             "gpu": sys_status_dict.get("gpu"), "gpuMem": sys_status_dict.get("gpu_mem"), }
        redis.hset(RedisKey.POD_SYS_INFO, CntEnvConstant.POD_IP, json.dumps(sys_status_format))


def count_pod_task():
    try:
        resp = http_client.post("http://127.0.0.1:3001/api/task/status/all")
        if resp.status_code == 200:
            task_list = resp.json().get("data", {})
            task_count = len(task_list.get("list", []))
            Vs2cCacheHandler.cache_pod_task_count(CntEnvConstant.POD_IP, task_count, is_init=True)

            now = datetime.now()
            logger.info(f"pod {CntEnvConstant.POD_IP} 更新任务数成功，当前任务数为: {task_count}, time:{now}")
            if now.second == 0:  # 直接获取当前秒数并判断
                time_str = now.strftime("%H:%M")

                used = json.dumps({"pod_ip": CntEnvConstant.POD_NAME, "count": task_count})
                Vs2cCacheHandler.cache_resource_used_by_date(time_str + '|' + CntEnvConstant.POD_NAME, used)

    except Exception as e:
        logger.error(f"pod 更新状态失败,错误原因为 {e}")


# 初始化 APScheduler
scheduler = BlockingScheduler()
# 5分钟执行一次
scheduler.add_job(check_detect_service_status, trigger=IntervalTrigger(seconds=10 * 60))
scheduler.add_job(set_pod_ex_time, trigger=IntervalTrigger(seconds=5))
scheduler.add_job(count_pod_task, trigger=IntervalTrigger(seconds=1))

try:
    logger.info("Starting scheduler...")
    scheduler.start()
except (KeyboardInterrupt, SystemExit):
    logger.info("Scheduler shutting down...")
    scheduler.shutdown()
