# GET /internal/v1/snaptasks/:id 接口实现文档

## 实现概述

成功实现了 `GET /internal/v1/snaptasks/{task_id}` 接口，用于查询指定ID的抽帧任务详细信息。该接口提供了完整的任务信息查询功能，包括任务配置、运行状态、流信息等。

## 接口详情

### 基本信息
- **URL**: `/internal/v1/snaptasks/{task_id}`
- **方法**: `GET`
- **参数**: `task_id` - 要查询的任务ID（路径参数）
- **Content-Type**: `application/json`

### 请求示例

```bash
# 查询指定任务
curl -X GET "http://localhost:3001/internal/v1/snaptasks/task_20241201_001" \
     -H "Content-Type: application/json"
```

### 响应格式

#### 成功查询 (200)
```json
{
  "code": 200,
  "msg": "ok",
  "data": {
    "id": "task_20241201_001",
    "stream": {
      "id": "stream_camera_01",
      "source": "rtmp_camera",
      "url": "rtmp://*************:1935/live/camera01",
      "urlRefreshCallback": null,
      "urlRefreshInterval": 0,
      "urlRefreshStreamPath": null
    },
    "interval": 30.0,
    "status": 1,
    "createdTime": 1701398400.0,
    "processPod": "worker-node-01"
  }
}
```

#### 任务不存在 (404)
```json
{
  "code": 404,
  "msg": "任务不存在"
}
```

#### 非抽帧任务 (400)
```json
{
  "code": 400,
  "msg": "不是抽帧任务"
}
```

#### 服务器错误 (500)
```json
{
  "code": 500,
  "msg": "查询任务失败: 具体错误信息"
}
```

## 核心功能实现

### 1. 任务验证
- 检查任务是否存在于Redis缓存中
- 验证任务类型是否为抽帧任务（通过检查`stream`字段）
- 返回相应的错误信息如果验证失败

### 2. 状态映射
- 从Redis获取任务状态信息
- 将内部状态映射为API标准状态码：
  - 0: 待启动 (pending)
  - 1: 运行中 (running)
  - 2: 已完成 (complete)
  - 3: 异常 (failed)

### 3. 数据构建
- 解析Redis中存储的任务内容
- 构建标准化的响应数据结构
- 包含完整的流配置和任务元数据

### 4. 日志记录
- 记录查询操作的详细信息
- 包含请求来源、任务ID、操作结果等
- 便于问题排查和操作审计

## 实现代码

```python
@app.get("/internal/v1/snaptasks/{task_id}")
async def get_snap_task(task_id: str, request: Request):
    """获取抽帧任务详细信息接口"""
    try:
        # 检查任务是否存在
        existing_task = Vs2cCacheHandler.get_task_content(task_id)
        if not existing_task:
            logger.warning(f"任务 {task_id} 不存在")
            return {"code": 404, "msg": "任务不存在"}
        
        # 解析任务内容
        task_dict = json.loads(existing_task)
        
        # 检查是否是snap任务
        if not task_dict.get('stream'):
            logger.warning(f"任务 {task_id} 不是抽帧任务")
            return {"code": 400, "msg": "不是抽帧任务"}
        
        # 获取任务状态
        task_status_info = Vs2cCacheHandler.get_task_status(task_id)
        status = 0  # 默认待启动
        process_pod = None
        
        if task_status_info:
            # 状态映射
            status_mapping = {
                TaskStatus.pending: 0,
                TaskStatus.running: 1, 
                TaskStatus.complete: 2,
                TaskStatus.failed: 3
            }
            status = status_mapping.get(task_status_info.get("status"), 0)
            process_pod = task_status_info.get("pod_ip")
        
        # 构建响应数据
        response_data = {
            "id": task_dict["id"],
            "stream": {
                "id": task_dict["stream"]["id"],
                "source": task_dict["stream"]["source"],
                "url": task_dict["stream"]["url"],
                "urlRefreshCallback": task_dict["stream"].get("urlRefreshCallback"),
                "urlRefreshInterval": task_dict["stream"].get("urlRefreshInterval", 0),
                "urlRefreshStreamPath": task_dict["stream"].get("urlRefreshStreamPath")
            },
            "interval": task_dict["interval"],
            "status": status,
            "createdTime": time.time(),
            "processPod": process_pod
        }
        
        logger.info(f"成功查询抽帧任务: {task_id}, 请求来源于：{request.headers.get('host')}")
        
        return {"code": 200, "msg": "ok", "data": response_data}
        
    except Exception as e:
        logger.error(f"查询抽帧任务失败: {task_id}, 错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"查询任务失败: {str(e)}"}
```

## 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | string | 任务唯一标识符 |
| `stream` | object | 流信息对象 |
| `stream.id` | string | 流的唯一标识 |
| `stream.source` | string | 流来源标识 |
| `stream.url` | string | 视频流地址 |
| `stream.urlRefreshCallback` | string/null | 流地址刷新回调URL |
| `stream.urlRefreshInterval` | integer | 刷新间隔（秒） |
| `stream.urlRefreshStreamPath` | string/null | JSON路径 |
| `interval` | float | 截图间隔（秒） |
| `status` | integer | 任务状态 |
| `createdTime` | float | 创建时间戳 |
| `processPod` | string/null | 处理节点标识 |

## 状态码说明

| 状态码 | 含义 | 描述 |
|--------|------|------|
| 0 | 待启动 | 任务已创建但尚未开始执行 |
| 1 | 运行中 | 任务正在执行抽帧操作 |
| 2 | 已完成 | 任务执行完成 |
| 3 | 异常 | 任务执行过程中发生错误 |

## 错误处理

### 1. 任务不存在
- **场景**: 指定的task_id在系统中不存在
- **响应**: 404状态码，明确的错误信息
- **处理**: 记录警告日志，返回失败状态

### 2. 任务类型错误
- **场景**: 任务存在但不是抽帧任务类型
- **响应**: 400状态码，类型错误信息
- **处理**: 避免返回其他类型任务的信息

### 3. 系统异常
- **场景**: 查询过程中发生未预期的错误
- **响应**: 500状态码，详细错误信息
- **处理**: 记录完整的异常堆栈，便于调试

## 测试验证

### 1. Python测试脚本
```python
# 在 test_snap_task.py 中包含查询测试
def test_get_snap_task_api():
    task_id = "task_20241201_001"
    url = f"http://localhost:3001/internal/v1/snaptasks/{task_id}"
    response = requests.get(url, timeout=10)
    # 验证响应...
```

### 2. cURL测试脚本
```bash
# test_snap_task_curl.sh
curl -X GET "http://localhost:3001/internal/v1/snaptasks/task_20241201_001" \
     -H "Content-Type: application/json"
```

### 3. 完整流程测试
- 创建任务 → 验证创建成功
- 查询任务 → 验证返回正确信息
- 删除任务 → 验证删除成功
- 再次查询 → 验证返回404错误

## 与Go版本的对比

| 功能特性 | Go版本 | Python版本 | 说明 |
|---------|--------|------------|------|
| 任务查询 | ✅ | ✅ | 都支持任务信息查询 |
| 状态映射 | ✅ | ✅ | 都提供状态码映射 |
| 数据转换 | ✅ | ✅ | 都转换为标准响应格式 |
| 缓存查询 | ✅ | ✅ | 都从Redis缓存查询 |
| 数据库查询 | ✅ | ❌ | Python版本暂未实现数据库查询 |
| 错误处理 | ✅ | ✅ | 都有完整的错误处理 |

## 部署和使用

### 1. 启动服务
```bash
python api.py
```

### 2. 测试接口
```bash
# 运行Python测试
python test_snap_task.py

# 运行cURL测试
./test_snap_task_curl.sh
```

### 3. 监控日志
- 查看查询操作日志
- 监控错误和异常
- 验证响应数据格式

## 注意事项

1. **任务ID有效性**: 确保传入正确的任务ID
2. **状态实时性**: 状态信息来自Redis缓存，具有实时性
3. **数据一致性**: 确保任务内容和状态数据的一致性
4. **权限控制**: 考虑添加权限验证机制
5. **性能优化**: 查询操作已优化，响应速度快

## 后续优化建议

1. **数据库集成**: 添加数据库查询支持，提供历史数据
2. **缓存策略**: 优化缓存策略，提高查询性能
3. **批量查询**: 支持一次查询多个任务
4. **字段过滤**: 支持指定返回字段，减少数据传输
5. **分页支持**: 为列表查询添加分页功能
