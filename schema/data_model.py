from typing import List, Optional

import enum
from pydantic import BaseModel, Field, root_validator

from common.typevars import TaskStatus, AiAnalyseMode, TaskPlatform


class BaseConfigModel(BaseModel):
    minWidth: int = Field(description="目标框最小宽度", default=10)
    minHeight: int = Field(description="目标框最小高度", default=10)
    confidence: float = Field(description="目标框最小置信度", default=0.6)
    enabled: bool = Field(description="是否开启目标检测", default=True)
    regionArray: list = Field(description="检测区域", default=[])
    # mode: dict = Field(description="检测模式", default={})


class QualityModel(BaseConfigModel):
    # 是否遮挡 0: 无遮挡，1: 轻微遮挡(默认值)，2: 重度遮挡( = 无遮挡 + 所有遮挡 + 未知)
    occlusion: str = Field(description="遮挡", default=1)
    # 是否截断 0: 无截断(默认值), 1: 有截断(包含所有截断，不包含未知), 2: 不限制( = 无截断 + 所有截断＋末知)
    truncation: str = Field(description="截断", default=0)
    # 清晰度0->清晰，1->轻度模糊，2->重度模糊
    clear: int = Field(description="清晰度", default=0)  # 清晰度


class FaceModel(QualityModel):
    # 抓拍人脸角度 0: 只抓拍正脸(默认)；1: 有人即抓拍
    # captureAngle = Field(description="抓拍人脸角度", default=0)
    qaScore = Field(description="抓拍人脸质量", default=0.3)

    # completeness 0->完整人脸 1-> 不完整人脸
    # completeness = Field(description="抓拍人脸完整度", default=0)
    # iFqaScore =
    # pitch: float = Field(description="pitch", default=30)
    # roll: float = Field(description="roll", default=30)
    # yaw: float = Field(description="yaw", default=30)

    # integrity: int = Field(description="完整度", default=30)  # 完整度

    @root_validator
    def update_from_mode(cls, values):
        # mode = values.get('mode', {})
        # capture_angle = values.get("captureAngle", 1)
        # capture_condition = values.get("captureCondition", {})
        # angle_values = {}
        # if int(capture_angle) == 1:
        #     angle_values['pitch'] = [-90, 90]
        #     angle_values['roll'] = [-90, 90]
        #     angle_values['yaw'] = [-90, 90]
        # else:
        #     if capture_condition:
        #         angle_values = {k[1:].lower(): v for k, v in capture_condition.items() if k.startswith("a")}
        #     else:
        #         angle_values['pitch'] = [-30, 30]
        #         angle_values['roll'] = [-30, 30]
        #         angle_values['yaw'] = [-30, 30]

        # values['captureCondition'] = angle_values
        # completeness = values.get("completeness", 0)
        # if completeness == 0:
        #     values["truncation"] = 0
        #     values["occlusion"] = 0
        # elif completeness == 1:
        #     values["truncation"] = 1
        #     values["occlusion"] = 1
        return values


class BodyModel(QualityModel):
    pass
    # @root_validator
    # def update_from_mode(cls, values):
    #     mode = values.get('mode', {})
    #     mode_value = mode.get("value", 2)
    #     values['occlusion'] = int(mode_value)
    #     return values


class VehicleModel(QualityModel):
    plateEnable: int = Field(description="是否启用车牌置信度", default=0)
    plateConfidence: float = Field(description="车牌置信度", default=0.6)


class NoneVehicleModel(QualityModel):
    pass


class Stream2captureType(str, enum.Enum):
    ONLINE = "liveStream"  # 实时视频流
    OFFLINE = "offlineVideo"  # 离线视频流


class DetectObject(BaseModel):
    left: float
    top: float
    width: float
    height: float
    confidence: float
    trackUuid: str


class DetectObjectList(BaseModel):
    items: List[DetectObject] = Field(default=[])
    nums: int = Field(default=0)
    frameId: int = Field(default=0)


class SystemAppInfo(BaseModel):
    id: str
    secret: str


class SnapshotConf(BaseModel):
    enabled: bool = Field(default=True)
    frameRate: int = Field(default=5)
    appFrameRate: int = Field(default=5)


class DetectConf(BaseModel):
    face: FaceModel
    body: BodyModel
    nonVehicle: NoneVehicleModel
    vehicle: VehicleModel


class TaskConfig(BaseModel):
    taskId: Optional[str]
    env: str = Field(default="算法环境")
    channel: Optional[str] = Field("通道号")
    deviceSN: str = Field(description="设备序列号")
    captureTtl: str = Field(description="存储时长")
    systemAppInfo: SystemAppInfo
    # snapshotConf: SnapshotConf
    detectConf: DetectConf
    appId: str = Field(description="数据所属的appid")
    aiseEnvWebhooksUrl: str = Field(description="数据所属的appid")
    imageQfactor: Optional[int] = Field(description="大图压缩比例", default=30)
    mainTaskId: Optional[str] = Field(default="", description="主的任务id")
    ivmsProjectId: Optional[str] = Field(description="ivms的任务id")

    # @root_validator
    # def verify_data(cls, values):
    #     if values.get("stream2captureType") == Stream2captureType.OFFLINE:
    #         assert values.get("deviceSN"), "离线视频分析需要提供设备sn"
    #
    #     if values.get("stream2captureType") == Stream2captureType.ONLINE:
    #         assert values.get("streamId"), "在线视频分析需要提供设备streamId"
    #         assert values.get("deviceId"), "在线视频分析需要提供设备deviceId"
    #
    #     return values


class TaskDefinition(BaseModel):
    deviceSN: str = Field(description="设备序列号")
    taskId: str = Field(description="任务id")
    mainTaskId: Optional[str] = Field(default="", description="主任务id")
    name: str = Field(description="任务名称")
    channel: Optional[str] = Field(description="通道编号")
    videoUrl: str = Field(description="视频url")
    videoTime: Optional[int] = Field(description="视频开始时间")
    videoDuration: Optional[int] = Field(description="视频总长度")
    taskPlatform: Optional[TaskPlatform] = Field(description="", default=TaskPlatform.r200)
    rate: float = Field(description="视频帧率")
    appRate: Optional[float] = Field(description="mode6特有基础云抽帧频率")
    statusWebhookUrl: str
    snapshotWebhookUrl: str
    status: TaskStatus = Field(default=TaskStatus.pending)
    progress: int = Field(default=0)
    mode: Optional[AiAnalyseMode]
    cluster: Optional[bool]
    alarm: Optional[bool]
    ivmsEnvWebhooksUrl: Optional[str]
    tenantId: Optional[str]

    @root_validator
    def handle_null(cls, values):
        if values.get("taskPlatform") is None:
            values["taskPlatform"] = TaskPlatform.r200
        return values


class AllocateNode(BaseModel):
    tenantId: str = Field(description="商户id")
    nodeIds: list = Field(description="需要操作的节点")
    userId: Optional[str] = Field(description="用户ID")


class StreamInfo(BaseModel):
    id: str = Field(..., description="流的唯一标识")
    source: str = Field(..., description="流来源标识")
    url: str = Field(..., description="视频流地址")
    urlRefreshCallback: Optional[str] = Field(None, description="流地址刷新回调URL（可选）")
    urlRefreshInterval: Optional[int] = Field(0, description="刷新间隔（秒，0表示不刷新）")
    urlRefreshStreamPath: Optional[str] = Field(None, description="JSON路径，用于从回调响应中提取新的流地址")


class SnapTaskRequest(BaseModel):
    id: str = Field(..., description="任务唯一标识符")
    stream: StreamInfo = Field(..., description="流信息")
    interval: float = Field(..., description="截图间隔（秒）")
    forwardReq: bool = Field(False, description="是否为转发请求（通常设为false）")
