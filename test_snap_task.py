#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试抽帧任务接口的脚本
"""

import json
import requests
import time

def test_snap_task_api():
    """测试抽帧任务API"""
    
    # 测试数据
    test_data = {
        "id": "task_20241201_001",
        "stream": {
            "id": "stream_camera_01",
            "source": "rtmp_camera",
            "url": "rtmp://*************:1935/live/camera01"
        },
        "interval": 30.0,
        "forwardReq": False
    }
    
    # API端点
    url = "http://localhost:3001/internal/v1/snaptasks"
    
    try:
        print("发送抽帧任务请求...")
        print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=test_data, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 抽帧任务创建成功!")
                print(f"任务ID: {result.get('data', {}).get('taskId')}")
            else:
                print(f"❌ 任务创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务正在运行在 localhost:3001")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_get_snap_task_api():
    """测试查询抽帧任务API"""

    # 要查询的任务ID
    task_id = "task_20241201_001"

    # API端点
    url = f"http://localhost:3001/internal/v1/snaptasks/{task_id}"

    try:
        print("发送查询抽帧任务请求...")
        print(f"任务ID: {task_id}")

        response = requests.get(url, timeout=10)

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 抽帧任务查询成功!")
                data = result.get('data', {})
                print(f"任务ID: {data.get('id')}")
                print(f"流URL: {data.get('stream', {}).get('url')}")
                print(f"间隔: {data.get('interval')}秒")
                print(f"状态: {data.get('status')}")
                print(f"处理节点: {data.get('processPod')}")
            else:
                print(f"❌ 任务查询失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务正在运行在 localhost:3001")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_delete_snap_task_api():
    """测试删除抽帧任务API"""

    # 要删除的任务ID
    task_id = "task_20241201_001"

    # API端点
    url = f"http://localhost:3001/internal/v1/snaptasks/{task_id}"

    try:
        print("发送删除抽帧任务请求...")
        print(f"任务ID: {task_id}")

        response = requests.delete(url, timeout=10)

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 抽帧任务删除成功!")
                print(f"删除状态: {result.get('data', {}).get('status')}")
            else:
                print(f"❌ 任务删除失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务正在运行在 localhost:3001")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_different_intervals():
    """测试不同的间隔时间"""
    intervals = [1.0, 5.0, 10.0, 30.0, 60.0, 300.0]  # 1秒, 5秒, 10秒, 30秒, 1分钟, 5分钟

    for i, interval in enumerate(intervals):
        test_data = {
            "id": f"task_interval_test_{i+1}",
            "stream": {
                "id": f"stream_test_{i+1}",
                "source": "test_camera",
                "url": "rtmp://test.example.com/live/test"
            },
            "interval": interval,
            "forwardReq": False
        }

        print(f"\n测试间隔 {interval} 秒的任务...")
        expected_fps = 1.0 / interval
        print(f"预期帧率: {expected_fps:.6f} fps")

        # 这里只是打印测试数据，不实际发送请求
        print(f"测试数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")


def test_create_get_delete_workflow():
    """测试创建、查询和删除任务的完整流程"""

    # 测试数据
    task_id = "task_workflow_test_001"
    create_data = {
        "id": task_id,
        "stream": {
            "id": "stream_workflow_test",
            "source": "test_camera",
            "url": "rtmp://test.example.com/live/workflow"
        },
        "interval": 15.0,
        "forwardReq": False
    }

    create_url = "http://localhost:3001/internal/v1/snaptasks"
    get_url = f"http://localhost:3001/internal/v1/snaptasks/{task_id}"
    delete_url = f"http://localhost:3001/internal/v1/snaptasks/{task_id}"

    print("=== 测试创建、查询和删除任务的完整流程 ===")

    try:
        # 1. 创建任务
        print("\n1. 创建任务...")
        create_response = requests.post(create_url, json=create_data, timeout=10)
        print(f"创建响应: {create_response.status_code} - {create_response.text}")

        if create_response.status_code == 200:
            create_result = create_response.json()
            if create_result.get("code") == 200:
                print("✅ 任务创建成功")

                # 2. 等待一下
                print("\n2. 等待2秒...")
                time.sleep(2)

                # 3. 查询任务
                print("\n3. 查询任务...")
                get_response = requests.get(get_url, timeout=10)
                print(f"查询响应: {get_response.status_code} - {get_response.text}")

                if get_response.status_code == 200:
                    get_result = get_response.json()
                    if get_result.get("code") == 200:
                        print("✅ 任务查询成功")
                        data = get_result.get('data', {})
                        print(f"   任务ID: {data.get('id')}")
                        print(f"   间隔: {data.get('interval')}秒")
                        print(f"   状态: {data.get('status')}")
                    else:
                        print(f"❌ 任务查询失败: {get_result.get('msg')}")
                else:
                    print(f"❌ 查询请求失败: {get_response.status_code}")

                # 4. 删除任务
                print("\n4. 删除任务...")
                delete_response = requests.delete(delete_url, timeout=10)
                print(f"删除响应: {delete_response.status_code} - {delete_response.text}")

                if delete_response.status_code == 200:
                    delete_result = delete_response.json()
                    if delete_result.get("code") == 200:
                        print("✅ 任务删除成功")
                        print("✅ 完整流程测试通过")
                    else:
                        print(f"❌ 任务删除失败: {delete_result.get('msg')}")
                else:
                    print(f"❌ 删除请求失败: {delete_response.status_code}")
            else:
                print(f"❌ 任务创建失败: {create_result.get('msg')}")
        else:
            print(f"❌ 创建请求失败: {create_response.status_code}")

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务正在运行在 localhost:3001")
    except Exception as e:
        print(f"❌ 流程测试失败: {e}")

if __name__ == "__main__":
    print("=== 抽帧任务API测试 ===\n")

    # 测试创建功能
    print("1. 测试创建抽帧任务")
    test_snap_task_api()

    print("\n" + "="*50)
    print("2. 测试查询抽帧任务")
    test_get_snap_task_api()

    print("\n" + "="*50)
    print("3. 测试删除抽帧任务")
    test_delete_snap_task_api()

    print("\n" + "="*50)
    print("4. 测试创建、查询和删除完整流程")
    test_create_get_delete_workflow()

    print("\n" + "="*50)
    print("5. 不同间隔时间测试")
    test_different_intervals()

    print("\n测试完成!")
