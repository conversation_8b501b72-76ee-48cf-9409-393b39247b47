# coding: utf-8
import os
import time
import json
import aiofiles
import aioboto3
import asyncio
import logging
from typing import List
from Crypto.Cipher import AES
from binascii import b2a_hex, a2b_hex
from s3_sdk.utils import RegexMap
from s3_sdk.s3_conf import EncryptAgent, S3Conf, generate_current_secret_s3_regex_conf

from aiobotocore.config import AioConfig

from settings import CntEnvConstant

ONLINE_CONTENT_TYPE = {"image/png", "image/jpeg"}
session = aioboto3.Session()


class S3BaseClient(object):

    def __init__(
            self,
            bucket_name: str = CntEnvConstant.S3_PUBLIC_BUCKET_NAME,
            access_key: str = CntEnvConstant.S3_PUBLIC_ACCESS_KEY,
            secret_key: str = CntEnvConstant.S3_PUBLIC_SECRET_KEY,
            region_name: str = CntEnvConstant.S3_PUBLIC_REGEION,
            public_endpoint_url: str = CntEnvConstant.S3_PUBLIC_REGEION_HOST_URL,
            private_endpoint_url: str = CntEnvConstant.S3_PRIVATE_REGEION_HOST_URL,
            sign_style: str = CntEnvConstant.S3_PUBLIC_SIGN_STYLE,
            signature_version: str = CntEnvConstant.S3_PUBLIC_SIGN_VERSION,
            s3_encrypt_agent: EncryptAgent = None
    ):
        self.service_name = "s3"
        self.bucket_name = bucket_name
        self.access_key = access_key
        self.secret_key = secret_key
        self.region_name = region_name
        self.public_endpoint_url = public_endpoint_url
        self.private_endpoint_url = private_endpoint_url
        self.sign_style = sign_style
        self.signature_version = signature_version
        if self.sign_style == "host":
            self.config = AioConfig(signature_version=self.signature_version, s3={'addressing_style': 'virtual'})
        else:
            self.config = AioConfig(signature_version=self.signature_version, s3={'addressing_style': 'path'})
        if s3_encrypt_agent is None:
            s3_encrypt_agent = EncryptAgent()
        self.s3_encrypt_agent = s3_encrypt_agent

    async def create_bucket(self, bucket_name: str, acl: str = "public-read"):
        """创建桶"""
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            try:
                await resource.create_bucket(
                    Bucket=bucket_name,
                    ACL=acl,
                    CreateBucketConfiguration={'LocationConstraint': self.region_name},
                )
                logging.info(f'S3 bucket "{bucket_name}" created successfully.')
            except resource.meta.client.exceptions.BucketAlreadyOwnedByYou:
                logging.info(f'S3 bucket "{bucket_name}" already exists and is owned by you.')

    async def put_bucket_policy_public_read(self, bucket_name: str):
        """桶策略设置公共读"""
        async with session.client(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as client:
            policy_json = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Sid": "PublicReadGetObject",
                        "Effect": "Allow",
                        "Principal": "*",
                        "Action": "s3:GetObject",
                        "Resource": f"arn:aws:s3:::{bucket_name}/*"
                    },
                ]
            }
            await client.put_bucket_policy(Bucket=bucket_name, Policy=json.dumps(policy_json))
            logging.info(f'S3 bucket "{bucket_name}" put_bucket_policy public_read')

    async def lifecycle_conf(self, dir_prefix_name: str, expires_in: int):
        """ 配置对应的object所在目录的生命周期

        :param dir_prefix_name: <str>, like: "snapshots"  # 表示为哪个目录配置生命周期, 注意: 此处不带 / 后缀
        :param expires_in: <int>, like: 1  # 表示1天的生命周期
        :return:
        """
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.public_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            bucket_lifecycle = await resource.BucketLifecycle(self.bucket_name)
            response = await bucket_lifecycle.put(
                # ChecksumAlgorithm='SHA256',
                LifecycleConfiguration={
                    'Rules': [
                        {
                            'ID': f'{dir_prefix_name}{expires_in}DayRule',  # snapshots1DayRule
                            # "Filter": {
                            #     "Prefix": f"{dir_prefix_name}/",
                            # },
                            "Prefix": f"{dir_prefix_name}/",
                            'Status': 'Enabled',
                            'AbortIncompleteMultipartUpload': {
                                'DaysAfterInitiation': expires_in
                            },
                            'Expiration': {
                                'Days': expires_in,
                            },
                        },
                    ]
                },
            )
            return response

    async def get_lifecycle(self):
        """ 查看对应的object所在目录的生命周期
        :return:
        """
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.public_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            bucket_lifecycle = await resource.BucketLifecycle(self.bucket_name)
            return await bucket_lifecycle.rules

    async def list_objects_url_with_prefix(self, key_prefix: str = "") -> List:
        """ 根据前缀匹配，返回命中的object的url列表

        :param key_prefix:
        :return:
        """
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            bucket = await resource.Bucket(self.bucket_name)
            return [
                await self.sign_s3_agent_url(s3_key=s3_object.key)
                async for s3_object in bucket.objects.filter(Prefix=key_prefix)
            ]

    async def list_objects_url_with_paginate(self, next_token: str = "", key_prefix: str = "", page: int = 1,
                                             size: int = 10) -> List:
        async with session.client(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as client:
            paginator = client.get_paginator('list_objects_v2')
            response_iterator = paginator.paginate(
                Bucket=self.bucket_name,
                Prefix=key_prefix,
                PaginationConfig={
                    'MaxItems': size,
                    'PageSize': page,
                    'StartingToken': next_token if next_token else None
                }
            )
            obj_list = [obj async for obj in response_iterator]
            return [
                {
                    'Key': obj['Contents'][0]['Key'],
                    'NextContinuationToken': obj['NextContinuationToken'] if 'NextContinuationToken' in obj else ''
                } for obj in obj_list
            ]

    async def delete_object(self, s3_key: str = ""):
        return await self.delete_objects(s3_keys=[s3_key])

    async def delete_objects(self, s3_keys: List[str]) -> None:
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            bucket = await resource.Bucket(self.bucket_name)
            delete_keys = {'Objects': [{'Key': v} for v in s3_keys]}
            resp = await bucket.delete_objects(Delete=delete_keys)
            return resp

    async def download_file(self, s3_key, file_name):
        """通过S3下载存储地址  file_name：下载文件地址目录，使用绝对路径，/tmp/test.txt"""

        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            await resource.meta.client.download_file(Bucket=self.bucket_name, Key=s3_key, Filename=file_name)

    async def copy_item2new_key(self, old_s3_key, new_s3_key):
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.public_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            # copy旧对象，生成一个新的对象
            copy_source = {'Bucket': self.bucket_name, 'Key': old_s3_key}
            await resource.meta.client.copy(
                CopySource=copy_source,
                Bucket=self.bucket_name,
                Key=new_s3_key
            )
            # 删除旧对象
            await self.delete_objects(s3_keys=[old_s3_key])
            return await self.sign_s3_agent_url(s3_key=new_s3_key, expires_in=7 * 24 * 3600 - 1)

    async def multipart_upload_part(self, s3_key: str, content: bytes, upload_id: str, part_number: int):
        async with session.client(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as client:
            response = await client.upload_part(
                Bucket=self.bucket_name,
                Key=s3_key,
                UploadId=upload_id,
                PartNumber=int(part_number),
                Body=content
            )
            return response

    async def put_object(self, s3_key: str, content: bytes, expires_in: int = 7 * 24 * 3600 - 1, content_type=None):
        """ 上传文件bytes

        :param s3_key:
        :param content:
        :param expires_in:
        :param content_type:
        :return:
        """
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            bucket = await resource.Bucket(self.bucket_name)
            kwargs = dict(ACL='public-read', Key=s3_key, Body=content)
            if content_type:
                kwargs.update(ContentType=content_type)
                if content_type in ONLINE_CONTENT_TYPE:  # 在线访问图片, aliyun doesn't work?
                    kwargs.update(ContentDisposition="inline")

            await bucket.put_object(**kwargs)
            # public-read
            # return await self.sign_url_with_splicing(s3_key=s3_key)

            # private-read
            # return await self.generate_pre_signed_url(s3_key=s3_key, expires_in=expires_in)

            # agent-public&private-read
            return await self.sign_s3_agent_url(s3_key=s3_key, expires_in=expires_in)

    async def put_object_from_file(self, s3_key: str, file_abspath: str, expires_in: int = 7 * 24 * 3600 - 1):
        """ 从pod内的绝对路径上传对象到对象存储

        :param s3_key:
        :param file_abspath:
        :param expires_in:
        :return:
        """
        if not os.path.exists(file_abspath):
            raise FileNotFoundError(f"不存在 {file_abspath} 文件!")

        async with aiofiles.open(file_abspath, "rb") as fp:
            _url = await self.put_object(s3_key=s3_key, content=await fp.read())

        return _url

    async def put_object_from_bin_data(
            self, s3_key, bin_data, content_type: str = None, expires_in: int = 7 * 24 * 3600 - 1):
        """ 直接上传 binData
        """
        _url = await self.put_object(s3_key=s3_key, content=bin_data, content_type=content_type, expires_in=expires_in)
        return _url

    async def create_multipart_upload(self, s3_key):
        async with session.client(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as client:
            response = await client.create_multipart_upload(Bucket=self.bucket_name, Key=s3_key)
            return response

    async def complete_multipart_upload(self, s3_key: str, upload_id: str, parts: list):
        async with session.client(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as client:
            response = await client.complete_multipart_upload(
                Bucket=self.bucket_name,
                Key=s3_key,
                UploadId=upload_id,
                MultipartUpload={'Parts': parts}
            )
            return response

    async def list_multipart_uploads(self, prefix: str, upload_id: str):
        async with session.client(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as client:
            response = await client.list_multipart_uploads(Bucket=self.bucket_name, Prefix=prefix)
            uploads = response.get('Uploads', [])
            for upload in uploads:
                if upload['UploadId'] == upload_id:
                    print(f"Upload exists with Key: {upload['Key']}, Initiator: {upload['Initiator']}")
            return uploads

    # 原始带签名的url，不可被agent_url替代
    async def generate_pre_signed_url(self, s3_key: str = "", expires_in: int = 7 * 24 * 3600 - 1,
                                      method: str = "GET") -> str:
        """ s3v2各大对象存储厂商已弃用，s3v4官方仅支持7天签名有效期的signature url, 所以此处需要各厂商sdk重写此方法

        :param s3_key:
        :param expires_in:
        :param method: 签名请求方法, 范围为: 'PUT', 'HEAD', 'DELETE', 'GET'
        :return:
        """
        req_method = method.lower()
        assert req_method in ["put", "head", "delete", "get"], "s3 仅支持 PUT, HEAD, DELETE, GET 方法签名"
        async with session.resource(
                service_name=self.service_name,
                region_name=self.region_name,
                endpoint_url=self.private_endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=self.config
        ) as resource:
            return await resource.meta.client.generate_presigned_url(
                ClientMethod=f'{req_method}_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': s3_key,
                },
                ExpiresIn=expires_in
            )

    async def sign_url_with_splicing(self, s3_key):
        """
        self.public_endpoint_url: https://s3.dianjun.sensoro.vip
        path:
        """
        if not s3_key:
            return ''

        if s3_key.startswith("/"):
            s3_key = s3_key.lstrip('/')

        return await self.sign_s3_agent_url(s3_key)

    async def sign_s3_agent_url(self, s3_key, expires_in: int = 7 * 24 * 3600 - 1):
        if s3_key.startswith("/"):
            s3_key = s3_key.lstrip('/')

        expires_in = expires_in or int(time.time()) + 24 * 3600

        # 2024年3月8日前
        plain_text = f"{self.bucket_name}||{expires_in}||{self.private_endpoint_url}"

        # 2024年3月8日后
        plain_text = f"{self.bucket_name}||{expires_in}||{self.private_endpoint_url}||{s3_key}"

        # 2024年4月12日后
        plain_text = f"{self.bucket_name}||{s3_key}"

        _signature = self.s3_encrypt_agent.encrypt_s3plain_text2aes_signature(plain_text=plain_text)

        return f"{self.s3_encrypt_agent.agent_public_get_host}/{s3_key}?signature={_signature}&expires={expires_in}&bucket={self.bucket_name}&iHost={self.private_endpoint_url}"

    # 兼容ftp、mqtt等同步进程中对s3的需求
    def sync_sign_url(self, key, expires_in: int = 7 * 24 * 3600 - 1):
        """ object 必须公共读 才可使用此方法"""
        if not key:
            return ""

        key = key.lstrip('/')

        expires_in = expires_in or int(time.time()) + 24 * 3600

        # 2024年3月8日前
        plain_text = f"{self.bucket_name}||{expires_in}||{self.private_endpoint_url}"

        # 2024年3月8日后
        plain_text = f"{self.bucket_name}||{expires_in}||{self.private_endpoint_url}||{key}"

        # 2024年4月12日后
        plain_text = f"{self.bucket_name}||{key}"

        _signature = self.s3_encrypt_agent.encrypt_s3plain_text2aes_signature(plain_text=plain_text)

        return f"{self.s3_encrypt_agent.agent_public_get_host}/{key}?signature={_signature}&expires={expires_in}&bucket={self.bucket_name}&iHost={self.private_endpoint_url}"


class S3PublicDecryptAgent(EncryptAgent):
    """ 解密signature -> bucket, s3_host"""

    async def decrypt_aes_signature2s3_sdk(self, signature) -> S3BaseClient:
        """

        :param signature:
            2024年3月8日前：f"{self.bucket_name}||{expires_in}||{self.private_endpoint_url}"
            2024年3月8日后：f"{self.bucket_name}||{expires_in}||{self.private_endpoint_url}||{s3_key}"

        :return:
        """
        cryptos = AES.new(self.agent_aes_key.encode('utf-8'), AES.MODE_CBC, self.agent_aes_iv)

        plain_text = cryptos.decrypt(a2b_hex(signature.encode()))

        plain_text_str = bytes.decode(plain_text).rstrip('\0')

        # f"{self.bucket_name}||{expires_in}||{self.private_endpoint_url}"
        plain_text_list = plain_text_str.split("||")

        # 新版签名：2024年4月12日后
        if len(plain_text_list) == 2:
            s3_host = os.getenv("S3_AI_INTERNAL_ENDPOINT_URL")
            s3_bucket, s3_key = plain_text_list

        # 旧版签名: 2024年3月8日前
        elif len(plain_text_list) == 3:
            s3_bucket, expires_timestamp, s3_host = plain_text_list

        # 旧版签名: 2024年3月8日后
        elif len(plain_text_list) == 4:
            s3_bucket, expires_timestamp, s3_host, s3_key = plain_text_list

        else:
            raise IndexError(f"Signature: {signature} 解析长度不合法！")

        current_s3_conf: S3Conf
        current_s3_conf = RegexMap(
            generate_current_secret_s3_regex_conf(s3_host=s3_host, s3_bucket=s3_bucket), None
        )[s3_bucket]

        # todo: history_s3_conf: 阿里云生产环境，之前使用的阿里云oss对象存储，2024年4月底前，迁移到火山云tos对象存储，仍需保障之前阿里云对象存储的vsc-get的代理链接正常访问
        if not current_s3_conf:
            raise FileNotFoundError(404, "bucket 不存在!")

        return S3BaseClient(
            bucket_name=current_s3_conf.bucket_name,
            access_key=current_s3_conf.access_key,
            secret_key=current_s3_conf.secret_key,
            region_name=current_s3_conf.region_name,
            public_endpoint_url=current_s3_conf.private_endpoint_url,
            private_endpoint_url=current_s3_conf.private_endpoint_url,
            sign_style=current_s3_conf.sign_style,
            signature_version=current_s3_conf.signature_version,
        )


if __name__ == '__main__':
    import asyncio
    from urllib.parse import urlparse, parse_qs

    # 西宁环境
    s3_sdk = S3BaseClient(
        bucket_name="aise-dev1",
        region_name="cn-beijing",
        access_key="AKLTMjg2MjVhZGFmOTI5NDExYmFiOGNlMTY3Y2IyYTYzMWQ",
        secret_key="T1dVMFptWmlNRFpoWXpKa05HWTFNbUU1WVdFM05EWXhORGxtWlRkbU5XTQ==",
        # public_endpoint_url="https://tos-s3-cn-beijing.volces.com",
        # private_endpoint_url="https://tos-s3-cn-beijing.volces.com",
        # public_endpoint_url="https://vsc-put-dev.sensoro.com",
        # private_endpoint_url="https://vsc-put-dev.sensoro.com",
        public_endpoint_url="http://127.0.0.1:8200",
        private_endpoint_url="http://127.0.0.1:8200",
        sign_style="path",
        signature_version="s3v4"
    )

    # 阿里云生产环境
    print("1111")
    url = asyncio.run(
        s3_sdk.put_object_from_file(
            s3_key="sys/vpn/ivms_openvpn.tar.gz.123321",
            file_abspath="/Users/<USER>/Desktop/logo24_sensoro1.bmp"
        )
    )
    print("2222", url)
