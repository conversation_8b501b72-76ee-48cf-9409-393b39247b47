# coding: utf-8
import datetime
import json
import os
import random
import time
import uuid
from collections import defaultdict

import requests
from starlette.background import BackgroundTasks

from call_back import start_task, call_back, stop_task, get_status, start_detect_and_track_task, \
    callback_detect_and_track, get_system_status, start_snap_task, check_snap_task
import uvicorn
from fastapi import FastAPI, Request
from common.typevars import TaskStatus, AiAnalyseMode, CallBackTaskStatus, TaskPlatform
from libs.redis_handler import init_redis_pool, Vs2cCacheHandler, RedisKey
from logger import logger
from filelock import FileLock
# 自研人脸检测算法

from schema.data_model import (

    Stream2captureType, TaskConfig, TaskDefinition, AllocateNode, SnapTaskRequest, CheckTaskRequest,

)
from settings import REQUEST_ID_CONTEXT, CntEnvConstant

app = FastAPI()
redis = init_redis_pool()
import httpx

# 创建全局客户端
client = httpx.AsyncClient()


def stop_current_task(task_id):
    stop_task(task_id)
    # Vs2cCacheHandler.cache_pod_task_count(CntEnvConstant.POD_IP, -1)
    return {"code": 200, "msg": "ok"}


async def redispatch_task(pod_name, task_content):
    logger.info(f"选择到的用户节点为:{pod_name}")
    ip_to_pod = redis.hgetall(RedisKey.POD_Ip2Name)
    reversed_dict = {value.decode('utf-8'): key.decode('utf-8') for key, value in ip_to_pod.items()}
    ip = reversed_dict.get(pod_name)
    if not ip:
        logger.info(f"pod_name:{pod_name} 对应的ip 不存在 ，不下发任务")
        return
    await client.post(f"http://{ip}:3001/api/task/video/process", json=task_content, timeout=2)


async def stop_running_task(task_id: str):
    task_status = Vs2cCacheHandler.get_task_status(task_id)
    if task_status:
        try:
            task_pod_ip = task_status["pod_ip"]
            exist_pod = Vs2cCacheHandler.get_pod_task_count(task_pod_ip)
            if exist_pod:
                logger.info(f"存在已运行的任务，停止已运行的任务 task id: {task_id} 运行的ip:{task_pod_ip} ")
                if task_pod_ip == CntEnvConstant.POD_IP:
                    stop_current_task(task_id)
                else:
                    await client.post(f"http://{task_pod_ip}:3001/api/task/stop", json={"taskId": task_id}, timeout=2)
            else:
                logger.info("pod不存在")

        except Exception as e:
            logger.error(f"下发任务停止失败，pod已经丢失 {e}", exc_info=True)


@app.on_event("startup")
async def set_pod_running_task_count():
    Vs2cCacheHandler.cache_pod_task_count(CntEnvConstant.POD_IP, count=0, is_init=True)
    ip_to_pod = redis.hgetall(RedisKey.POD_Ip2Name)
    for key, value in ip_to_pod.items():
        decoded_value = value.decode('utf-8')
        if decoded_value == CntEnvConstant.POD_NAME:
            logger.info("删除旧ip")
            redis.hdel(RedisKey.POD_Ip2Name, key.decode('utf-8'))

    redis.hset(RedisKey.POD_Ip2Name, CntEnvConstant.POD_IP, CntEnvConstant.POD_NAME)
    # # 创建一个文件锁，确保只有一个进程执行任务
    # lock = FileLock("/tmp/scheduler.lock")
    # try:
    #     with lock.acquire(timeout=1):
    #         logger.info("获取到锁，启动定时任务")
    #         start_scheduler()
    # except:
    #     logger("无法获取锁，另一个进程可能已在运行定时任务")


@app.on_event("shutdown")
async def shutdown():
    redis.hdel(RedisKey.POD_Ip2Name, CntEnvConstant.POD_IP)
    await client.aclose()


@app.post("/api/task/video/process")
async def process_video_task(task_content: TaskDefinition, request: Request):
    if task_content.tenantId:
        tenant_node = redis.hget(RedisKey.USER_NODE_KEY, task_content.tenantId)
        tenant_node = json.loads(tenant_node) if tenant_node else []
        user_pod_list = [i['nodeId'] for i in tenant_node]
        if not user_pod_list:
            return {"code": 400, "msg": "该商户尚未分配该资源"}
        if not CntEnvConstant.POD_NAME in user_pod_list:
            await redispatch_task(random.choice(user_pod_list), task_content.dict())
            logger.info("非租户节点，重新下发任务")
            return {"code": 200, "msg": "ok"}

    current_task_count = Vs2cCacheHandler.get_pod_task_count(CntEnvConstant.POD_IP)
    REQUEST_ID_CONTEXT.set(task_content.taskId)
    if task_content.mode == AiAnalyseMode.advanced_reanalyse_ai_mode:
        Vs2cCacheHandler.add_sub_record_task(task_content.mainTaskId, task_content.taskId)

    running_task = Vs2cCacheHandler.get_task_content(task_content.taskId)
    # 查看当前任务是否已经运行，如果运行的话停止该任务重新下发
    if task_content.mode == AiAnalyseMode.smart_schedule_ai_mode or running_task:
        logger.info(f"存在运行中的任务停止该任务")
        # 只有 mode 4 时才考虑子任务，停止任务并删除任务
        if task_content.mode == AiAnalyseMode.smart_schedule_ai_mode:
            logger.info(f"任务的mode为：{task_content.mode}，停止该任务及其子任务")
            # 停止主任务
            await stop_running_task(task_content.taskId)
            # 停止并删除所有子任务
            if task_content.mainTaskId:
                sub_task_ids = Vs2cCacheHandler.get_sub_record_task(task_content.mainTaskId)
                if sub_task_ids:
                    for sub_task_id in sub_task_ids:
                        await stop_running_task(sub_task_id)
                        Vs2cCacheHandler.del_task_content(sub_task_id)
                    Vs2cCacheHandler.del_sub_record_task(task_content.mainTaskId)
            # 删除主任务内容
            Vs2cCacheHandler.del_task_content(task_content.taskId)
            return {"code": 200, "msg": "ok"}
        else:
            # 非mode4，仅停止主任务
            await stop_running_task(task_content.taskId)
    task_config = Vs2cCacheHandler.get_task_config(task_content.deviceSN, task_content.mainTaskId)

    if not task_config:
        logger.info("设备配置不存在，不执行任务")
        Vs2cCacheHandler.del_task_content(task_content.taskId)
        return {"code": 400, "msg": "设备配置不存在，不执行任务"}

    # 数值需要具体看定义
    if not current_task_count or int(current_task_count) < int(CntEnvConstant.MAX_PROCESS):
        task_content.status = TaskStatus.running

        task_config = json.loads(task_config)

        start_task(task_content, task_config)
        # Vs2cCacheHandler.cache_pod_task_count(CntEnvConstant.POD_IP, 1)
        Vs2cCacheHandler.cache_task_status(task_content.taskId,
                                           json.dumps({"status": TaskStatus.running, "pod_ip": CntEnvConstant.POD_IP}))

    else:
        task_content.status = TaskStatus.pending
        logger.info("当前pod任务数已达到上限")

    logger.info(f"下发的抽帧任务为: {task_content},请求来源于：{request.headers.get('host')}")

    Vs2cCacheHandler.cache_task_content(task_content.taskId, json.dumps(task_content.dict()))
    return {"code": 200, "msg": "ok"}


@app.post("/api/task/config/set")
async def set_task_config(request_body: TaskConfig):
    task_config = request_body.dict()
    Vs2cCacheHandler.cache_task_config(request_body.deviceSN, json.dumps(task_config), request_body.mainTaskId)
    logger.info(f"下发的抽帧配置为: {task_config}")
    return {"code": 200, "msg": "ok"}


@app.post("/api/task/status/{task_id}")
async def get_task_status(task_id: str):
    if task_id == "all":
        resp = await get_status("")
    else:
        resp = await get_status(task_id)
    return {"code": 200, "msg": "ok", "data": json.loads(resp)}


@app.post("/api/task/callback")
async def get_task_status(request: Request, background_tasks: BackgroundTasks):
    resp = await request.json()
    logger.info(f"回调数据：{resp}")
    tasks = {
        callback_detect_and_track: resp.get("result") or resp.get("status") == CallBackTaskStatus.TASK_ANALYSIS_DONE,
        call_back: resp.get("result") is None
    }
    for task, condition in tasks.items():
        if condition:
            background_tasks.add_task(task, resp)
    return {"code": 200, "msg": "ok"}


@app.post("/api/task/stop")
async def execute_stop_task(request: Request):
    req = await request.json()
    return stop_current_task(req["taskId"])


@app.post("/api/node/sys")
async def get_node_sys_info(request: Request):
    return {"code": 200, "msg": "ok", "data": json.loads(get_system_status())}


@app.post("/api/detectAndTrack")
async def detect_and_track(request: Request):
    """
    {
        "videoUrl": "https://xxx",
        "fps": 7,
        "skipMode": 1,  # 1: 随机模式， 2: 平均模式
        "timestamp": 1729481010,
        "token": "xxxx",
        "callbackUrl": "https://dms.xn.sensoro.vip/dms/webhooks/v1/autotest/detectAndTrack/132/121/callback"
    }
    """
    task_content = await request.json()
    task_id = str(uuid.uuid4())
    task_content['taskId'] = task_id
    Vs2cCacheHandler.cache_task_content(task_id, json.dumps(task_content))
    start_detect_and_track_task(task_content)
    logger.info(f"下发的检测跟踪任务为:{task_content}")
    return {"code": 200, "msg": "ok"}


@app.post("/api/load/node")
async def load_node(request: Request):
    request_body = await request.json()
    tenant_id = request_body.get("tenantId")
    pod_keys = redis.keys(RedisKey.POD_TASK_COUNT.format(POD_IP="*"))
    pod_keys = [i.decode() for i in pod_keys]
    pod_list = [i.decode() for i in redis.mget(pod_keys)]
    pod_ips = [i.split(":")[-1] for i in pod_keys]
    pod_name_list = [i.decode() for i in redis.hmget(RedisKey.POD_Ip2Name, pod_ips) if i]
    pod_status_dict = {pod_ip.decode(): json.loads(pod_status) for pod_ip, pod_status in
                       redis.hgetall(RedisKey.POD_SYS_INFO).items()}

    result = []
    if tenant_id and tenant_id != "-1":
        tenant_node = redis.hget(RedisKey.USER_NODE_KEY, tenant_id)
        tenant_node = {i['nodeId']: i for i in json.loads(tenant_node)} if tenant_node else {}
        for pod_ip, pod_content, pod_name in zip(pod_ips, pod_list, pod_name_list):
            user_node = tenant_node.get(pod_name, {})
            if user_node:
                pod_status = pod_status_dict.get(pod_ip)
                result_item = {"tenantId": tenant_id, "nodeId": pod_name, "intervened": pod_content,
                               "total": CntEnvConstant.MAX_PROCESS,
                               "status": 1, "cpu": pod_status.get("cpu", 32.57),
                               "cpuMem": pod_status.get("cpuMem", 19.16), "gpu": pod_status.get("gpu", 52.0),
                               "gpuMem": pod_status.get("gpuMem", 38.92),
                               "userId": user_node['userId'], "created": user_node['created']}
                result.append(result_item)
        return {"code": 200, "msg": "ok", "data": result}
    else:
        tenant_node = redis.hgetall(RedisKey.USER_NODE_KEY)
        node_tenant_dict = {
            node["nodeId"]: {**node, "tenantId": tenant.decode()}  # 将 tenant 字段加入 node
            for tenant, node_list in tenant_node.items()  # 遍历 tenant_node
            for node in json.loads(node_list)  # 解析 node_list 为字典列表
        }
        for pod_ip, pod_content, pod_name in zip(pod_ips, pod_list, pod_name_list):
            node_tenant_item = node_tenant_dict.get(pod_name, {})
            pod_status = pod_status_dict.get(pod_ip, {})
            result_item = {"tenantId": node_tenant_item.get("tenantId"), "nodeId": pod_name, "intervened": pod_content,
                           "total": CntEnvConstant.MAX_PROCESS, "status": 1, "cpu": pod_status.get("cpu", 32.57),
                           "cpuMem": pod_status.get("cpuMem", 19.16),
                           "gpu": pod_status.get("gpu", 52.0),
                           "gpuMem": pod_status.get("gpuMem", 38.92),
                           "userId": node_tenant_item.get('userId'), "created": node_tenant_item.get('created')}
            if tenant_id != "-1" or not result_item.get("tenantId"):
                result.append(result_item)

        return {"code": 200, "msg": "ok", "data": result}


@app.post("/api/allocate/node")
async def del_node(request: Request, request_body: AllocateNode):
    tenant_node = redis.hget(RedisKey.USER_NODE_KEY, request_body.tenantId)
    node_list = []
    for node in request_body.nodeIds:
        node_item = {
            "nodeId": node,
            "created": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "userId": request_body.userId
        }
        node_list.append(node_item)

    tenant_node = json.loads(tenant_node) if tenant_node else []
    tenant_node.extend(node_list)
    redis.hset(RedisKey.USER_NODE_KEY, request_body.tenantId, json.dumps(tenant_node))
    return {"code": 200, "msg": "ok", "data": ""}


@app.post("/api/deallocate/node")
async def del_node(request: Request, request_body: AllocateNode):
    tenant_node = redis.hget(RedisKey.USER_NODE_KEY, request_body.tenantId)
    tenant_node = json.loads(tenant_node) if tenant_node else []
    insert_node = []
    for node in tenant_node:
        if not node["nodeId"] in request_body.nodeIds:
            insert_node.append(node)
    # result = [item for item in tenant_node if item not in request_body.nodeIds]
    redis.hset(RedisKey.USER_NODE_KEY, request_body.tenantId, json.dumps(insert_node))
    return {"code": 200, "msg": "ok", "data": ""}


@app.get("/api/task/stats/chart/{tenant_id}")
async def get_task_stats(tenant_id):
    all_task = Vs2cCacheHandler.load_task_content()
    # 初始化数据结构（确保所有字段存在）
    stats_by_platform = {
        "r200": {"realtime": 0, "record": 0, "used": 0, "total": 0, "unused": 0},
        "ax650": {"realtime": 0, "record": 0, "used": 0, "total": 0, "unused": 0},
        "total": {"total": 0, "used": 0, "percent": 0}
    }

    stats_by_type = {
        "realtime": {"r200": 0, "ax650": 0, "total": 0},
        "record": {"r200": 0, "ax650": 0, "total": 0}
    }
    stats_all_resource = {
        "total": {"realtime": 0, "record": 0, "video": 0, "intelligent_scheduling": 0, "used": 0, "total": 0,
                  "unused": 0},
    }
    tenant_r200_node = redis.hget(RedisKey.USER_NODE_KEY, tenant_id)
    tenant_r200_count = len(json.loads(tenant_r200_node)) * CntEnvConstant.MAX_PROCESS if tenant_r200_node else 0
    ax650_key = "stream2captureUserNode:vxiningax650prod"
    if "pre" in CntEnvConstant.SERVICE_BRANCH:
        ax650_key = "stream2captureUserNode:vxiningax650pre"
    tenant_ax650_node = redis.hget(ax650_key, tenant_id)
    tenant_ax650_count = len(
        json.loads(tenant_ax650_node)) * CntEnvConstant.MAX_PROCESS_AX650 if tenant_ax650_node else 0
    ax650_platform_name = "ax650"
    r200_platform_name = "r200"
    for _, task_json in all_task.items():
        task_dict = json.loads(task_json)
        platform = task_dict.get("taskPlatform")  # r200 或 ax650
        task_type = task_dict.get("mode")  # 实时分析/离线分析
        task_tenant_id = task_dict.get("tenantId")
        if platform is None or task_type is None or not tenant_id == task_tenant_id:
            continue  # 跳过无效数据
        platform_str = ax650_platform_name if platform == TaskPlatform.ax650 else r200_platform_name
        if task_type in [AiAnalyseMode.double_analyse_ai_mode,
                         # AiAnalyseMode.standard_ai_mode,
                         AiAnalyseMode.advanced_live_ai_mode,
                         # AiAnalyseMode.advanced_reanalyse_ai_mode
                         ]:
            task_type_str = "realtime"
        elif task_type in [AiAnalyseMode.fast_video_ai_mode, AiAnalyseMode.advanced_reanalyse_ai_mode]:
            task_type_str = "record"
        else:
            continue
        stats_by_platform[platform_str][task_type_str] += 1
        stats_by_platform[platform_str]["used"] += 1
        if task_type_str in stats_by_type:
            stats_by_type[task_type_str][platform_str] += 1
            stats_by_type[task_type_str]["total"] += 1
    tenant_total = 0
    tenant_used = 0
    for platform, count in [("r200", tenant_r200_count), ("ax650", tenant_ax650_count)]:
        stats_by_platform[platform]["total"] = count
        stats_by_platform[platform]["unused"] = max(count - stats_by_platform[platform]["used"], 0)
        tenant_total += count
        tenant_used += stats_by_platform[platform]["used"]
    stats_by_platform["total"]['total'] = tenant_total
    stats_by_platform["total"]['used'] = tenant_used
    stats_by_platform["total"]["percent"] = round(
        (stats_by_platform[platform]["used"] / stats_by_platform["total"]["total"]) * 100, 2) if \
        stats_by_platform["total"]["total"] else 0.00
    stats_all_resource["total"]["total"] = tenant_total
    stats_all_resource["total"]["used"] = tenant_used
    stats_all_resource["total"]["realtime"] = stats_by_type["realtime"]["total"]
    stats_all_resource["total"]["record"] = stats_by_type["record"]["total"]

    return {"code": 200, "msg": "ok", "data": {"statsByPlatform": stats_by_platform, "statsByType": stats_by_type,
                                               "statsAllResource": stats_all_resource}}


@app.get("/api/resource/used/line/{tenant_id}")
async def get_task_stats(tenant_id):
    tenant_node = redis.hget(RedisKey.USER_NODE_KEY, tenant_id)
    tenant_node = json.loads(tenant_node) if tenant_node else []
    user_pod_list = [i['nodeId'] for i in tenant_node]
    ax650_key = "stream2captureUserNode:vxiningax650prod"
    if "pre" in CntEnvConstant.SERVICE_BRANCH:
        ax650_key = "stream2captureUserNode:vxiningax650pre"
    tenant_ax650_node = redis.hget(ax650_key, tenant_id)
    tenant_ax650_count = len(json.loads(tenant_ax650_node)) if tenant_ax650_node else 0
    total_slots = len(tenant_node) * CntEnvConstant.MAX_PROCESS + tenant_ax650_count * CntEnvConstant.MAX_PROCESS_AX650
    use_pods = Vs2cCacheHandler.load_resource_used_by_date()
    result_dict = defaultdict(int)
    for pod_name, used_dict in use_pods.items():
        time_str, pod_name = pod_name.decode().split("|")
        if pod_name in user_pod_list:
            used_dict = json.loads(used_dict)
            result_dict[time_str] += used_dict['count']
    resource_line = {
        date: round((usage / total_slots) * 100, 2) if total_slots else 0.00  # 保留两位小数
        for date, usage in result_dict.items()
    }
    sorted_data = dict(sorted(resource_line.items(), key=lambda x: x[0]))
    resource_line = [
        {
            "date": date,
            "value": value
        }
        for date, value in sorted_data.items()
    ]
    return {"code": 200, "msg": "ok", "data": {"resourceUsedLine": resource_line}}


@app.post("/internal/v1/snaptasks")
async def create_snap_task(request_body: SnapTaskRequest, request: Request):
    """
    创建抽帧任务接口
    功能说明: 对请求体中的 stream.url 拉流并根据 interval 字段间隔抽帧

    请求体示例:
    {
      "id": "task_20241201_001",
      "stream": {
        "id": "stream_camera_01",
        "source": "rtmp_camera",
        "url": "rtmp://*************:1935/live/camera01"
      },
      "interval": 30.0,
      "forwardReq": false
    }
    """
    try:
        # 检查任务是否已存在
        existing_task = Vs2cCacheHandler.get_task_content(request_body.id)
        if existing_task:
            logger.info(f"任务 {request_body.id} 已存在，停止旧任务")
            # 停止已存在的任务
            stop_current_task(request_body.id)

        # 将请求体转换为字典格式
        task_content = request_body.dict()

        # 缓存任务内容
        Vs2cCacheHandler.cache_task_content(request_body.id, json.dumps(task_content))

        # 启动抽帧任务
        start_snap_task(task_content)

        # 缓存任务状态
        Vs2cCacheHandler.cache_task_status(request_body.id,
                                           json.dumps({"status": TaskStatus.running, "pod_ip": CntEnvConstant.POD_IP}))

        logger.info(f"下发的抽帧任务为: {task_content}, 请求来源于：{request.headers.get('host')}")

        return {"code": 200, "msg": "ok", "data": {"taskId": request_body.id}}

    except Exception as e:
        logger.error(f"创建抽帧任务失败: {e}", exc_info=True)
        return {"code": 500, "msg": f"创建任务失败: {str(e)}"}


@app.delete("/internal/v1/snaptasks/{task_id}")
async def delete_snap_task(task_id: str, request: Request):
    """
    删除抽帧任务接口

    Args:
        task_id: 要删除的任务ID

    Returns:
        删除操作的结果状态

    请求示例:
    DELETE /internal/v1/snaptasks/task_20241201_001

    响应示例:
    {
      "code": 200,
      "msg": "ok",
      "data": {
        "status": true
      }
    }
    """
    try:
        # 检查任务是否存在
        existing_task = Vs2cCacheHandler.get_task_content(task_id)
        if not existing_task:
            logger.warning(f"任务 {task_id} 不存在")
            return {"code": 404, "msg": "任务不存在", "data": {"status": False}}

        # 解析任务内容
        task_dict = json.loads(existing_task)

        # 检查是否是snap任务
        if not task_dict.get('stream'):
            logger.warning(f"任务 {task_id} 不是抽帧任务")
            return {"code": 400, "msg": "不是抽帧任务", "data": {"status": False}}

        # 停止任务
        stop_current_task(task_id)

        # 删除任务内容和状态
        Vs2cCacheHandler.del_task_content(task_id)
        Vs2cCacheHandler.del_task_status(task_id)

        logger.info(f"成功删除抽帧任务: {task_id}, 请求来源于：{request.headers.get('host')}")

        return {"code": 200, "msg": "ok", "data": {"status": True}}

    except Exception as e:
        logger.error(f"删除抽帧任务失败: {task_id}, 错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"删除任务失败: {str(e)}", "data": {"status": False}}


@app.get("/internal/v1/snaptasks/{task_id}")
async def get_snap_task(task_id: str, request: Request):
    """
    获取抽帧任务详细信息接口

    Args:
        task_id: 要查询的任务ID

    Returns:
        任务的详细信息

    请求示例:
    GET /internal/v1/snaptasks/task_20241201_001

    响应示例:
    {
      "code": 200,
      "msg": "ok",
      "data": {
        "id": "task_20241201_001",
        "stream": {
          "id": "stream_camera_01",
          "source": "rtmp_camera",
          "url": "rtmp://*************:1935/live/camera01",
          "urlRefreshCallback": null,
          "urlRefreshInterval": 0,
          "urlRefreshStreamPath": null
        },
        "interval": 30.0,
        "status": 1,
        "createdTime": 1701398400.0,
        "processPod": "worker-node-01"
      }
    }
    """
    try:
        # 检查任务是否存在
        existing_task = Vs2cCacheHandler.get_task_content(task_id)
        if not existing_task:
            logger.warning(f"任务 {task_id} 不存在")
            return {"code": 404, "msg": "任务不存在"}

        # 解析任务内容
        task_dict = json.loads(existing_task)

        # 检查是否是snap任务
        if not task_dict.get('stream'):
            logger.warning(f"任务 {task_id} 不是抽帧任务")
            return {"code": 400, "msg": "不是抽帧任务"}

        # 获取任务状态
        task_status_info = Vs2cCacheHandler.get_task_status(task_id)
        status = 0  # 默认待启动
        process_pod = None

        if task_status_info:
            # 状态映射: TaskStatus.pending=0, running=1, complete=2, failed=3
            status_mapping = {
                TaskStatus.pending: 0,
                TaskStatus.running: 1,
                TaskStatus.complete: 2,
                TaskStatus.failed: 3
            }
            status = status_mapping.get(task_status_info.get("status"), 0)
            process_pod = task_status_info.get("pod_ip")

        # 构建响应数据
        response_data = {
            "id": task_dict["id"],
            "stream": {
                "id": task_dict["stream"]["id"],
                "source": task_dict["stream"]["source"],
                "url": task_dict["stream"]["url"],
                "urlRefreshCallback": task_dict["stream"].get("urlRefreshCallback"),
                "urlRefreshInterval": task_dict["stream"].get("urlRefreshInterval", 0),
                "urlRefreshStreamPath": task_dict["stream"].get("urlRefreshStreamPath")
            },
            "interval": task_dict["interval"],
            "status": status,
            "createdTime": time.time(),  # 当前时间作为创建时间，实际应该从数据库获取
            "processPod": process_pod
        }

        logger.info(f"成功查询抽帧任务: {task_id}, 请求来源于：{request.headers.get('host')}")

        return {"code": 200, "msg": "ok", "data": response_data}

    except Exception as e:
        logger.error(f"查询抽帧任务失败: {task_id}, 错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"查询任务失败: {str(e)}"}


@app.post("/internal/v1/callbacks/snaptasks/check")
async def check_snapshot_task_callback(request_body: CheckTaskRequest, request: Request):
    """
    快照任务健康检查回调接口

    该接口由延时任务系统定期调用，用于：
    - 检查任务是否正常运行
    - 自动恢复异常任务
    - 实现跨节点的任务故障转移
    - 维护任务的持续监控

    Args:
        request_body: 检查任务请求体，包含完整的任务信息

    Returns:
        检查操作的结果状态

    请求示例:
    POST /internal/v1/callbacks/snaptasks/check
    {
      "id": "task_20241201_001",
      "clientId": "snap_job_check_task_20241201_001",
      "source": "delay_task",
      "body": {
        "jobId": "task_20241201_001",
        "stream": {
          "id": "stream_camera_01",
          "source": "rtmp_camera",
          "url": "rtmp://*************:1935/live/camera01"
        },
        "interval": 30.0,
        "createdTime": 1701398400000,
        "workerId": 1001
      }
    }

    响应示例:
    {
      "code": 0,
      "message": "success",
      "data": {
        "status": true
      }
    }
    """
    try:
        # 转换为字典格式，便于处理
        check_request_dict = {
            "id": request_body.id,
            "clientId": request_body.clientId,
            "source": request_body.source,
            "topic": request_body.topic,
            "callback": request_body.callback,
            "body": {
                "jobId": request_body.body.jobId,
                "stream": {
                    "id": request_body.body.stream.id,
                    "source": request_body.body.stream.source,
                    "url": request_body.body.stream.url,
                    "urlRefreshCallback": request_body.body.stream.urlRefreshCallback,
                    "urlRefreshInterval": request_body.body.stream.urlRefreshInterval,
                    "urlRefreshStreamPath": request_body.body.stream.urlRefreshStreamPath
                },
                "interval": request_body.body.interval,
                "createdTime": request_body.body.createdTime,
                "workerId": request_body.body.workerId
            }
        }

        # 异步执行检查逻辑
        await check_snap_task(check_request_dict)

        logger.info(f"成功处理抽帧任务检查回调: {request_body.body.jobId}, 请求来源于：{request.headers.get('host')}")

        # 立即返回成功响应（Go版本的格式）
        return {
            "code": 0,
            "message": "success",
            "data": {
                "status": True
            }
        }

    except Exception as e:
        logger.error(f"处理抽帧任务检查回调失败: {e}", exc_info=True)
        return {
            "code": 500,
            "message": f"检查任务失败: {str(e)}",
            "data": {
                "status": False
            }
        }


if __name__ == "__main__":
    uvicorn.run("api:app", host="0.0.0.0", port=3001, reload=True)
