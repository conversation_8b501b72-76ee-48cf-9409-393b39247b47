globalConfig:
  ANTMAN_APP_GROUP: vs2c
  ANTMAN_APP_NAME: vs2c-detect-and-track-cluster
  ANTMAN_APP_TYPE: python
  ANTMAN_APP_PORT: '3001'
  ANTMAN_DOCKER_BUILDKIT: enabled

deploy:
  # vs2c 3.0 服务
  - name: vs2c-detect-and-track-cluster
    monitor: enabled
    deploy_type: statefulset
    podManagementPolicy: "Parallel"
    command: [ "/bin/bash", "run_server.sh" ]


extra_container:
  - deploy: vs2c-detect-and-track-cluster

    # timede服务-有更新的化的化需要多构建一次，更新timed服务的版本
    container:
      - name: vs2c-detect-and-track-timed-sidecar
        image: harbor.xn.sensoro.vip/vxiningr200prod/vs2c-detect-and-track-cluster:vxiningr200pre-cad9bd5b-257
        imagePullPolicy: IfNotPresent
        volumeMounts: [ ]
        env:
          - name: CICD
            value: ANTMAN
          - name: RUN_ENV
            value: prod
          - name: DEPLOY_NAME
            value: vs2c-detect-and-track-cluster
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: POD_NODE_IP
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: POD_NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName

        envFrom: [ ]
        command: [ "/bin/bash", "run_timed.sh" ]

      - name: vs2c-detect-and-track-inference-sidecar
        image: harbor.xn.sensoro.vip/vxiningr200pre/vs2c-detect-and-track-cluster:vxiningr200pre-dd927aa9-264
        imagePullPolicy: IfNotPresent
        volumeMounts: [ ]
        env:
          - name: CICD
            value: ANTMAN
          - name: RUN_ENV
            value: prod
          - name: DEPLOY_NAME
            value: vs2c-detect-and-track-cluster
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: POD_NODE_IP
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: POD_NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName

        envFrom: [ ]
        resources:
          limits:
            baidu.com/xpu: '1'

        command: ["/bin/bash", "run_analysis_server.sh"]
        





extra_service:
  - deploy: vs2c-detect-and-track-cluster
    service:
      - port: 3001
        protocol: TCP
        targetPort: 3001
        name: vs2c-detect-and-track-cluster-3001


ingress:
  - type: api
    paths:
      - path: /
        deploy: vs2c-detect-and-track-cluster
        middlewares:
        - name: middleware-cors-common

  - type: wx
    paths:
      - path: /
        deploy: vs2c-detect-and-track-cluster
        middlewares:
         - name: middleware-cors-common


volumes:
  - emptyDir:
      medium: Memory
      sizeLimit: 32Gi
    name: dshm


volumeMounts:
  - mountPath: /dev/shm
    name: dshm



#resources:
#  limits:
#    baidu.com/xpu: '1'
#
##
#securityContext:
#  privileged: true
#

envFrom:
  - secretRef:
      name: secret-redis-vs2c
  - secretRef:
      name: secret-s3-ai
  - secretRef:
      name: secret-w2m-app-vs2c
