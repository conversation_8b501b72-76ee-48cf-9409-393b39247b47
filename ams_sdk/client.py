# coding: utf-8

import hashlib
import hmac
import json
import time
import uuid

import aiohttp

from common.typevars import DeviceType
from common.utils import retry
from settings import CntEnvConstant, REQUEST_ID_CONTEXT


class IvmsServiceClient:
    """文档: https://sensoro.yuque.com/xud1db/vxeta5/bt5rgx"""

    def __init__(self, app_id=CntEnvConstant.SYS_ID, app_secret=CntEnvConstant.SYS_SECRET):
        self.url_map = dict(DEVICE_INFO="/v3/device/queryDeviceInfo",
                            VIDEO_COVER_CALLBACK="/webhooks/vms/v1")  # 获取设备详情  # 视频截图回调
        self.app_id = app_id
        self.app_secret = app_secret
        self.server_addr = CntEnvConstant.IVMS_DEVICE_SERVICE_URI

        self.headers = {"Content-Type": "application/json"}  # 默认请求头

    def _generate_hmac_sha256(self, x_access_nonce, request_url, request_method, request_body):
        """生成signature"""
        message = x_access_nonce + request_method + request_url
        if request_body is not None:
            message += request_body

        key = self.app_secret.encode("utf-8")
        message = message.encode("utf-8")
        signature = hmac.new(key, message, digestmod=hashlib.sha256).hexdigest()
        return signature

    def _make_request_headers(self, request_url, request_method, request_body):
        """构造鉴权请求头"""
        x_access_nonce = str(int(time.time() * 1000))

        if request_body is None and not request_method == "GET":
            request_body = json.dumps({})

        signature = self._generate_hmac_sha256(
            x_access_nonce=x_access_nonce,
            request_url=request_url,
            request_method=request_method,
            request_body=request_body,
        )

        trace_id = REQUEST_ID_CONTEXT.get() if REQUEST_ID_CONTEXT.get() != "-" else str(uuid.uuid4())
        headers = {
            "X-ACCESS-ID": self.app_id,
            "X-ACCESS-NONCE": x_access_nonce,
            "X-ACCESS-SIGNATURE": signature,
            "X-TRACE-ID": trace_id,
        }
        self.headers.update(headers)  # 将鉴权信息更新至请求头

    @retry(times=2, delay=0.2)
    async def smart_request(self, url, body, method, headers) -> dict:
        async with aiohttp.ClientSession() as session:
            async with session.request(method=method, url=url, data=body, headers=headers) as resp:
                assert resp.status == 200, f"请求失败, {resp.status}, {resp.reason},{await resp.json()}"
                return await resp.json()

    async def base_request(self, method, url, body=None):
        if isinstance(body, (dict, list)):
            body = json.dumps(body)

        self._make_request_headers(request_url=url, request_method=method, request_body=body)
        return await self.smart_request(url=url, body=body, method=method, headers=self.headers)

    async def get_device_info_by_sn_or_id(self, sn: str = None, s_id: str = None, device_type: DeviceType = None):
        """
        :param: sn, <str>, 此为设备序列号，子服务维护了sn的，使用子服务的，子服务未维护的，设备服务会生成，优先使用子服务维护的sn
        return:
            {
                "id": 1,
                "appId": "设备所属应用ID",
                "tenantId": 1,
                "sourceId": 1,
                "sn": "设备序列号",
                "pid": "",
                "name": "设备名称",
                "type": "FACE_CAPTURE",
                "protocolType": "LINS",
                "address": "地址",
                "lng": 101.101,
                "lat": 39.93,
                "direction": 1,
                "orientations": 1,
                "channels": [],
                "sourceData": {}
            }

        """
        req = {}
        if sn and device_type == DeviceType.rtmp:
            req.update(sn=sn.replace("DFC-RZ-", ""))
        elif s_id and device_type == DeviceType.gb28181:  # 含设备ID的为国标类型设备
            req.update(sourceDeviceId=s_id, deviceProtocolType=4)
        else:
            raise ValueError("sn or s_id must be provided")
        response_body = await self.base_request(method="POST", url=self.server_addr + self.url_map["DEVICE_INFO"],
                                                body=req)
        return response_body.get("data", {})

    async def video_snapshot_cover_callback(self, snapshot_webhook_url: str, task_id: str, sn: str, cover_url: str):
        req = {
            "action": "on_video_snapshot_cover",
            "deviceSN": sn,
            "objectSignUrl": cover_url,
            "taskId": task_id,
        }
        response_body = await self.base_request(
            method="POST", url=snapshot_webhook_url, body=req
        )
        return response_body.get("data", {})

    async def video_snapshot_capture_callback(self, snapshot_webhook_url: str, task_id: str, capture_url: str,
                                              frame_rate: float, capture_time: int, app_frame_rate: float):
        req = {
            "action": "on_vs2c_task_result",
            "taskId": task_id,
            "timestamp": capture_time,
            "objectSignUrl": capture_url,
            "frameRate": frame_rate,
            "appFrameRate": app_frame_rate
        }
        response_body = await self.base_request(
            method="POST", url=snapshot_webhook_url, body=req
        )
        return response_body.get("data", {})

    async def video_snapshot_process_callback(self, snapshot_webhook_url: str, task_id: str, pos: float):
        req = {
            "action": "on_vs2c_task_process",
            "taskId": task_id,
            "process": pos,
        }
        response_body = await self.base_request(
            method="POST", url=snapshot_webhook_url, body=req
        )
        return response_body.get("data", {})

    async def video_snapshot_status_callback(self, snapshot_webhook_url: str, task_id: str, status: str):
        req = {
            "action": "on_vs2c_task_status",
            "taskId": task_id,
            "status": status}
        response_body = await self.base_request(
            method="POST", url=snapshot_webhook_url, body=req
        )
        return response_body.get("data", {})


sys_ivms_service = IvmsServiceClient(CntEnvConstant.SYS_ID, CntEnvConstant.SYS_SECRET)

if __name__ == "__main__":
    import asyncio

    loop = asyncio.get_event_loop()
    r = loop.run_until_complete(
        sys_ivms_service.video_snapshot_cover_callback(
            task_id="62dfa81b8d3ee3001956e10a", sn="SNABC", cover_url="http://baidu.com"
        )
    )
    print(r)
