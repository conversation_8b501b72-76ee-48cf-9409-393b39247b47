# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from grpc_client import analysis_service_pb2 as analysis__service__pb2

GRPC_GENERATED_VERSION = '1.68.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in analysis_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class OfflineAnalysisServerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.LoadModel = channel.unary_unary(
                '/OfflineAnalysisServer/LoadModel',
                request_serializer=analysis__service__pb2.LoadModelRequest.SerializeToString,
                response_deserializer=analysis__service__pb2.Empty.FromString,
                _registered_method=True)
        self.StartTask = channel.unary_unary(
                '/OfflineAnalysisServer/StartTask',
                request_serializer=analysis__service__pb2.StartTaskRequest.SerializeToString,
                response_deserializer=analysis__service__pb2.Empty.FromString,
                _registered_method=True)
        self.StopTask = channel.unary_unary(
                '/OfflineAnalysisServer/StopTask',
                request_serializer=analysis__service__pb2.StopTaskRequest.SerializeToString,
                response_deserializer=analysis__service__pb2.Empty.FromString,
                _registered_method=True)
        self.GetTaskStatus = channel.unary_unary(
                '/OfflineAnalysisServer/GetTaskStatus',
                request_serializer=analysis__service__pb2.GetTaskStatusRequest.SerializeToString,
                response_deserializer=analysis__service__pb2.GetTaskStatusResponse.FromString,
                _registered_method=True)
        self.GetSystemStatus = channel.unary_unary(
                '/OfflineAnalysisServer/GetSystemStatus',
                request_serializer=analysis__service__pb2.Empty.SerializeToString,
                response_deserializer=analysis__service__pb2.GetSystemStatusResponse.FromString,
                _registered_method=True)


class OfflineAnalysisServerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def LoadModel(self, request, context):
        """加载模型
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartTask(self, request, context):
        """开始任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopTask(self, request, context):
        """停止任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskStatus(self, request, context):
        """查询任务状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSystemStatus(self, request, context):
        """查询系统状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OfflineAnalysisServerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'LoadModel': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadModel,
                    request_deserializer=analysis__service__pb2.LoadModelRequest.FromString,
                    response_serializer=analysis__service__pb2.Empty.SerializeToString,
            ),
            'StartTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StartTask,
                    request_deserializer=analysis__service__pb2.StartTaskRequest.FromString,
                    response_serializer=analysis__service__pb2.Empty.SerializeToString,
            ),
            'StopTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StopTask,
                    request_deserializer=analysis__service__pb2.StopTaskRequest.FromString,
                    response_serializer=analysis__service__pb2.Empty.SerializeToString,
            ),
            'GetTaskStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskStatus,
                    request_deserializer=analysis__service__pb2.GetTaskStatusRequest.FromString,
                    response_serializer=analysis__service__pb2.GetTaskStatusResponse.SerializeToString,
            ),
            'GetSystemStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSystemStatus,
                    request_deserializer=analysis__service__pb2.Empty.FromString,
                    response_serializer=analysis__service__pb2.GetSystemStatusResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'OfflineAnalysisServer', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('OfflineAnalysisServer', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OfflineAnalysisServer(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def LoadModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/OfflineAnalysisServer/LoadModel',
            analysis__service__pb2.LoadModelRequest.SerializeToString,
            analysis__service__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/OfflineAnalysisServer/StartTask',
            analysis__service__pb2.StartTaskRequest.SerializeToString,
            analysis__service__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/OfflineAnalysisServer/StopTask',
            analysis__service__pb2.StopTaskRequest.SerializeToString,
            analysis__service__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTaskStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/OfflineAnalysisServer/GetTaskStatus',
            analysis__service__pb2.GetTaskStatusRequest.SerializeToString,
            analysis__service__pb2.GetTaskStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSystemStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/OfflineAnalysisServer/GetSystemStatus',
            analysis__service__pb2.Empty.SerializeToString,
            analysis__service__pb2.GetSystemStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
