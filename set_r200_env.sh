#!/bin/bash
export XTCL_TEMP_RCNN_ENV=1
export OPENCV_JPEG_DECODE_KLX=0 # !!! to disable opencv xpu jpeg decode, if not set, cpu memleak may happen now
export OPENCV_JPEG_ENCODE_KLX=0 # !!! to disable opencv xpu jpeg encode, if not set, cpu memleak may happen now
export XPUSIM_DEVICE_MODEL=KUNLUN2
export XTCL_L3_SIZE=67104768
export XTCL_AUTO_ALLOC_L3=1
export XTCL_PRINT_L3_PLAN=0
export XTCL_BUILD_DEBUG=0
export XTCL_DUMP_OP_ARGS=0
export XTCL_PROFILE=0
export XTCL_USE_FP16=1