#python3 /app/download_ai_model.py
#ln -s -f /lib/x86_64-linux-gnu/libffi.so.7.1.0 /root/miniconda/envs/python38_torch201_cuda/lib/libffi.so.7
export LD_LIBRARY_PATH=/app/shlib:$LD_LIBRARY_PATH
#nohup python3 /app/timed.py > apscheduler.log 2>&1 &
source set_r200_env.sh
wget https://ivms-ai-model-harbor.oss-cn-beijing.aliyuncs.com/EIS/offline_analysis_py/klx/soft_reset && chmod +x  soft_reset &&  ./soft_reset 0

./cluster -m models_r200 -p 12000