#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试抽帧任务接口的脚本
"""

import json
import requests
import time

def test_snap_task_api():
    """测试抽帧任务API"""
    
    # 测试数据
    test_data = {
        "id": "task_20241201_001",
        "stream": {
            "id": "stream_camera_01",
            "source": "rtmp_camera",
            "url": "rtmp://*************:1935/live/camera01"
        },
        "interval": 30.0,
        "forwardReq": False
    }
    
    # API端点
    url = "http://localhost:3001/internal/v1/snaptasks"
    
    try:
        print("发送抽帧任务请求...")
        print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=test_data, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 抽帧任务创建成功!")
                print(f"任务ID: {result.get('data', {}).get('taskId')}")
            else:
                print(f"❌ 任务创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务正在运行在 localhost:3001")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_different_intervals():
    """测试不同的间隔时间"""
    intervals = [1.0, 5.0, 10.0, 30.0, 60.0, 300.0]  # 1秒, 5秒, 10秒, 30秒, 1分钟, 5分钟
    
    for i, interval in enumerate(intervals):
        test_data = {
            "id": f"task_interval_test_{i+1}",
            "stream": {
                "id": f"stream_test_{i+1}",
                "source": "test_camera",
                "url": "rtmp://test.example.com/live/test"
            },
            "interval": interval,
            "forwardReq": False
        }
        
        print(f"\n测试间隔 {interval} 秒的任务...")
        expected_fps = 1.0 / interval
        print(f"预期帧率: {expected_fps:.6f} fps")
        
        # 这里只是打印测试数据，不实际发送请求
        print(f"测试数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")

if __name__ == "__main__":
    print("=== 抽帧任务API测试 ===\n")
    
    # 测试基本功能
    test_snap_task_api()
    
    print("\n" + "="*50)
    print("=== 不同间隔时间测试 ===")
    
    # 测试不同间隔
    test_different_intervals()
    
    print("\n测试完成!")
