# DELETE /internal/v1/snaptasks/:id 接口实现文档

## 实现概述

成功实现了 `DELETE /internal/v1/snaptasks/{task_id}` 接口，用于删除指定ID的抽帧任务。该接口提供了完整的任务删除功能，包括任务验证、进程停止、资源清理等。

## 接口详情

### 基本信息
- **URL**: `/internal/v1/snaptasks/{task_id}`
- **方法**: `DELETE`
- **参数**: `task_id` - 要删除的任务ID（路径参数）
- **Content-Type**: `application/json`

### 请求示例

```bash
# 删除指定任务
curl -X DELETE "http://localhost:3001/internal/v1/snaptasks/task_20241201_001" \
     -H "Content-Type: application/json"
```

### 响应格式

#### 成功删除 (200)
```json
{
  "code": 200,
  "msg": "ok",
  "data": {
    "status": true
  }
}
```

#### 任务不存在 (404)
```json
{
  "code": 404,
  "msg": "任务不存在",
  "data": {
    "status": false
  }
}
```

#### 非抽帧任务 (400)
```json
{
  "code": 400,
  "msg": "不是抽帧任务",
  "data": {
    "status": false
  }
}
```

#### 服务器错误 (500)
```json
{
  "code": 500,
  "msg": "删除任务失败: 具体错误信息",
  "data": {
    "status": false
  }
}
```

## 核心功能实现

### 1. 任务验证
- 检查任务是否存在于Redis缓存中
- 验证任务类型是否为抽帧任务（通过检查`stream`字段）
- 返回相应的错误信息如果验证失败

### 2. 进程停止
- 调用 `stop_current_task(task_id)` 停止正在运行的抽帧进程
- 通过gRPC客户端与底层分析服务通信
- 确保FFmpeg等相关进程被正确终止

### 3. 资源清理
- 删除Redis中的任务内容：`Vs2cCacheHandler.del_task_content(task_id)`
- 删除Redis中的任务状态：`Vs2cCacheHandler.del_task_status(task_id)`
- 清理所有相关的缓存数据

### 4. 日志记录
- 记录删除操作的详细信息
- 包含请求来源、任务ID、操作结果等
- 便于问题排查和操作审计

## 实现代码

```python
@app.delete("/internal/v1/snaptasks/{task_id}")
async def delete_snap_task(task_id: str, request: Request):
    """删除抽帧任务接口"""
    try:
        # 检查任务是否存在
        existing_task = Vs2cCacheHandler.get_task_content(task_id)
        if not existing_task:
            logger.warning(f"任务 {task_id} 不存在")
            return {"code": 404, "msg": "任务不存在", "data": {"status": False}}

        # 解析任务内容
        task_dict = json.loads(existing_task)

        # 检查是否是snap任务
        if not task_dict.get('stream'):
            logger.warning(f"任务 {task_id} 不是抽帧任务")
            return {"code": 400, "msg": "不是抽帧任务", "data": {"status": False}}

        # 停止任务
        stop_current_task(task_id)

        # 删除任务内容和状态
        Vs2cCacheHandler.del_task_content(task_id)
        Vs2cCacheHandler.del_task_status(task_id)

        logger.info(f"成功删除抽帧任务: {task_id}, 请求来源于：{request.headers.get('host')}")

        return {"code": 200, "msg": "ok", "data": {"status": True}}

    except Exception as e:
        logger.error(f"删除抽帧任务失败: {task_id}, 错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"删除任务失败: {str(e)}", "data": {"status": False}}
```

## 错误处理

### 1. 任务不存在
- **场景**: 指定的task_id在系统中不存在
- **响应**: 404状态码，明确的错误信息
- **处理**: 记录警告日志，返回失败状态

### 2. 任务类型错误
- **场景**: 任务存在但不是抽帧任务类型
- **响应**: 400状态码，类型错误信息
- **处理**: 避免误删其他类型的任务

### 3. 系统异常
- **场景**: 删除过程中发生未预期的错误
- **响应**: 500状态码，详细错误信息
- **处理**: 记录完整的异常堆栈，便于调试

## 测试验证

### 1. Python测试脚本
```python
# 在 test_snap_task.py 中包含删除测试
def test_delete_snap_task_api():
    task_id = "task_20241201_001"
    url = f"http://localhost:3001/internal/v1/snaptasks/{task_id}"
    response = requests.delete(url, timeout=10)
    # 验证响应...
```

### 2. cURL测试脚本
```bash
# test_snap_task_curl.sh
curl -X DELETE "http://localhost:3001/internal/v1/snaptasks/task_20241201_001" \
     -H "Content-Type: application/json"
```

### 3. 完整流程测试
- 创建任务 → 验证创建成功
- 删除任务 → 验证删除成功
- 再次删除 → 验证返回404错误

## 与Go版本的对比

| 功能特性 | Go版本 | Python版本 | 说明 |
|---------|--------|------------|------|
| 任务验证 | ✅ | ✅ | 都支持任务存在性检查 |
| 类型验证 | ✅ | ✅ | 都验证任务类型 |
| 进程停止 | ✅ | ✅ | 都停止相关进程 |
| 跨节点转发 | ✅ | ❌ | Python版本暂未实现 |
| 资源清理 | ✅ | ✅ | 都清理相关资源 |
| 错误处理 | ✅ | ✅ | 都有完整的错误处理 |

## 部署和使用

### 1. 启动服务
```bash
python api.py
```

### 2. 测试接口
```bash
# 运行Python测试
python test_snap_task.py

# 运行cURL测试
./test_snap_task_curl.sh
```

### 3. 监控日志
- 查看删除操作日志
- 监控错误和异常
- 验证资源清理情况

## 注意事项

1. **任务ID唯一性**: 确保传入正确的任务ID
2. **权限控制**: 考虑添加权限验证机制
3. **并发安全**: 删除操作是原子性的
4. **资源清理**: 确保所有相关资源都被正确清理
5. **日志审计**: 所有删除操作都有详细日志记录

## 后续优化建议

1. **跨节点支持**: 实现分布式环境下的任务删除
2. **批量删除**: 支持一次删除多个任务
3. **软删除**: 考虑实现软删除机制
4. **权限控制**: 添加用户权限验证
5. **操作审计**: 增强操作日志和审计功能
