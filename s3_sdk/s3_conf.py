# coding: utf-8
import os
from Crypto.Cipher import AES
from binascii import b2a_hex, a2b_hex
from s3_sdk.utils import RegexMap


class S3Conf(object):

    def __init__(
            self,
            bucket_name,
            private_endpoint_url,
            public_endpoint_url,
            access_key,
            secret_key,
            region_name,
            sign_style,
            signature_version
    ):
        self.bucket_name: str = bucket_name
        self.private_endpoint_url = private_endpoint_url
        self.public_endpoint_url = public_endpoint_url
        self.access_key = access_key
        self.secret_key = secret_key
        self.region_name = region_name
        self.sign_style = sign_style
        self.signature_version = signature_version


def generate_current_secret_s3_regex_conf(s3_host, s3_bucket):
    s3_regex_conf = {
        # 旧版配置，仍需兼容：AISE: 固件上行的抓拍、日志、抓拍视频、配置文件等，桶会切换
        r"aise-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=s3_host,  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_AI_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_AI_ACCESS_KEY"),
            secret_key=os.getenv("S3_AI_SECRET_KEY"),
            region_name=os.getenv("S3_AI_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'
            signature_version="s3v4"
        ),

        # 旧版配置，仍需兼容：DMS: DMS数据管理平台上的存储桶，存储数据标注数据，桶不会切换
        r"dms-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=s3_host,  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_AI_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_AI_ACCESS_KEY"),
            secret_key=os.getenv("S3_AI_SECRET_KEY"),
            region_name=os.getenv("S3_AI_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'
            signature_version="s3v4"
        ),

        # 旧版配置，仍需兼容：公安视图库平台上的存储桶，存储公安上行的数据，桶会切换
        r"gat1400-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=s3_host,  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_AI_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_AI_ACCESS_KEY"),
            secret_key=os.getenv("S3_AI_SECRET_KEY"),
            region_name=os.getenv("S3_AI_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'
            signature_version="s3v4"
        ),

        # 新版配置
        r"ivms-aise-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=s3_host,  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_AI_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_AI_ACCESS_KEY"),
            secret_key=os.getenv("S3_AI_SECRET_KEY"),
            region_name=os.getenv("S3_AI_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'
            signature_version="s3v4"
        ),

        # IVMS: AMS上的业务存储桶，告警录像、告警图片、告警事件、用户上传的布控图片等永久存储， 桶不会切换（含：ivms-offline-video桶）
        r"ivms-ams-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=s3_host,  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_AI_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_AI_ACCESS_KEY"),
            secret_key=os.getenv("S3_AI_SECRET_KEY"),
            region_name=os.getenv("S3_AI_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'
            signature_version="s3v4"
        ),

        # VMS: VMS视频流媒体上的存储桶，存储封面图片、视频录像，周期删除，桶会切换
        r"ivms-vms-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=os.getenv("S3_VMS_INTERNAL_ENDPOINT_URL"),  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_VMS_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_VMS_ACCESS_KEY"),
            secret_key=os.getenv("S3_VMS_SECRET_KEY"),
            region_name=os.getenv("S3_VMS_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'  # 要求：vms始终应和ams、aise统一对象存储服务商
            signature_version="s3v4"
        ),
        r"ivms-vs2c-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=s3_host,  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_AI_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_AI_ACCESS_KEY"),
            secret_key=os.getenv("S3_AI_SECRET_KEY"),
            region_name=os.getenv("S3_AI_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'
            signature_version="s3v4"
        ),
        # VMS: VMS视频流媒体上的存储桶，存储封面图片、视频录像，周期删除，桶会切换
        r"ivms-dms-.*?": S3Conf(
            bucket_name=s3_bucket,  # 防止换桶，但是鉴权信息要统一
            private_endpoint_url=s3_host,  # 防止换对象存储，但是要求两个对象存储的ak、sk统一
            public_endpoint_url=os.getenv("S3_AI_PUBLIC_ENDPOINT_URL"),
            access_key=os.getenv("S3_AI_ACCESS_KEY"),
            secret_key=os.getenv("S3_AI_SECRET_KEY"),
            region_name=os.getenv("S3_AI_REGION"),
            sign_style=os.getenv("S3_AI_HOST_OR_PATH_STYLE", "host"),  # 'host' or 'path'
            signature_version="s3v4"
        ),
    }
    return s3_regex_conf


class EncryptAgent(object):
    """ AES对称加密，加密s3_bucket, s3_host"""

    def __init__(self, public_get_host: str = None, public_put_host: str = None):
        self.agent_aes_key = "XoN3rxG83CmQNdiT"  # 禁止更换
        self.agent_aes_iv = b"Lqo0WdG65HpFPvuN"  # 禁止更换
        if not public_get_host:
            public_get_host = os.getenv(
                "AMS_VSC_GET_URL", "https://vsc-get-dev.sensoro.com"
            )
        if not public_put_host:
            public_put_host = os.getenv(
                "AMS_VSC_PUT_URL", "https://vsc-put-dev.sensoro.com"
            )

        self.agent_public_get_host = public_get_host
        self.agent_public_put_host = public_put_host

    def encrypt_s3plain_text2aes_signature(self, plain_text):

        def add_to_16(text):
            if len(text.encode('utf-8')) % 16:
                add = 16 - (len(text.encode('utf-8')) % 16)
            else:
                add = 0

            text = text + ('\0' * add)
            return text.encode('utf-8')

        cryptos = AES.new(self.agent_aes_key.encode('utf-8'), AES.MODE_CBC, self.agent_aes_iv)

        padding_text = add_to_16(plain_text)
        cipher_text = cryptos.encrypt(padding_text)

        # 因为AES加密问题，所以这里转为16进制字符串
        return b2a_hex(cipher_text).decode()
