# coding: utf-8
""" 开发环境默认变量

"""

import os
import random

from .base import BaseConstant


base_dir = os.path.dirname(os.path.dirname(__file__))


class CntEnvConstant(BaseConstant):



    REQUEST_ID_CONTEXT = "-"
    FFMPEG_SERVICE_HOST = os.environ.get('FFMPEG_SERVICE_HOST', 'http://vs2c-ffmpeg-service/ffmpegService/')
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost/1")
    BACKEND_REDIS_HOST = os.getenv("REDIS_VS2C_HOST")
    BACKEND_REDIS_PORT = os.getenv("REDIS_VS2C_PORT")
    BACKEND_REDIS_DB = os.getenv("REDIS_VS2C_NO")
    BACKEND_REDIS_PASS = os.getenv("REDIS_VS2C_PASSWD")
    BACKEND_REDIS_URL = os.getenv("REDIS_VS2C_URI")
    POD_IP = os.getenv("POD_IP")
    POD_NAME = os.getenv("POD_NAME")
    SERVICE_BRANCH = os.getenv("ANTMAN_BRANCH")
    S3_PUBLIC_ACCESS_KEY = os.getenv('S3_AI_ACCESS_KEY', 'ESOFAMZEHZSNABSLWLDB')
    S3_PUBLIC_SECRET_KEY = os.getenv('S3_AI_SECRET_KEY', 'tYrqUkkfqFXD9M9aT15ajzCkhWLs6UoPJCFDllCl')
    S3_PUBLIC_REGEION_HOST_URL = os.getenv('S3_AI_PUBLIC_ENDPOINT_URL', 'https://vsc-get-wx.xn.sensoro.vip')
    S3_PUBLIC_REGEION = os.getenv('S3_AI_REGION', 'cn-beijing')
    S3_PUBLIC_BUCKET_NAME = os.getenv('S3_AI_BUCKET_NAME_VS2C', "aise-prod")
    S3_PUBLIC_SIGN_STYLE = os.getenv('S3_AI_HOST_OR_PATH_STYLE', 'host')
    S3_PUBLIC_SIGN_VERSION = os.getenv('S3_AI_SIGNATURE_VERSION', 'v4')
    S3_PRIVATE_REGEION_HOST_URL = os.getenv('S3_AI_INTERNAL_ENDPOINT_URL',
                                            'https://s3.xn1a.stor.xn.sensoro.vip')
    AI_MODEL_BASE_DIR = base_dir
    AI_DEVICE = 1  # get_available_gpu_device_id()
    GPU_USED_PERCENT = float(os.getenv("W2M_APP_VS2C_GPU_USED_PERCENT", 0.2))
    ANALYSIS_SERVICE_HOST = os.getenv("ANALYSIS_SERVICE_HOST", "0.0.0.0:12000")

    RUN_ENV = os.getenv("RUN_ENV", "local")
    MAX_PROCESS = os.getenv("MAX_PROCESS", 32)
    MAX_PROCESS_AX650 = os.getenv("MAX_PROCESS_AX650", 8)

    DETECT_SERVICE_COUNT = int(os.getenv("VS2C_DETECT_SERVICE_COUNT", 5))

    # redis
    REDIS_TESTING = RUN_ENV in ("local", "unit_test")

    # ivms
    IVMS_DEVICE_SERVICE_URI = os.getenv("W2M_APP_VS2C_AMS_HOST", "https://ivms-dev1-api.sensoro.com")

    # aise
    AISE_DEVICE_SERVICE_URI = os.getenv("W2M_APP_VS2C_AISE_HOST", "https://aise-dev1-api.sensoro.com")

    # system
    SYS_ID = os.getenv('W2M_APP_VS2C_APP_ID')
    SYS_SECRET = os.getenv('W2M_APP_VS2C_APP_SECRET')

    # uvicorn workers
    UVICORN_WORKERS = int(os.getenv("UVICORN_WORKERS", 6))

    # 算法 V2.0

    ALLOBJECT_DETECT_MODEL_ONNX_PATH = f"{base_dir}/dl_model/yolov5AllobjectDetectv1.2.onnx"  # 自研全目标检测模型 大图

    ALLOBJECT_DETECT_MODEL_TRT_PATH = f"{base_dir}/dl_model/yolov5AllobjectDetect_TR_v1.2.fp32.trtmodel"  # 自研全目标检测模型 大图

    # 算法鉴权
    SENSORO_OPENAI_API_ID = ""
    SENSORO_OPENAI_API_SK = ""
